import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  Timestamp,
  DocumentData,
  setDoc
} from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { SavedProject, CreateProjectData, ChapterDocument, CreateChapterData } from '@shared/types';
import { onAuthStateChanged, User } from 'firebase/auth';

const PROJECTS_COLLECTION = 'projects';
const CHAPTERS_SUBCOLLECTION = 'chapters';

/**
 * Get the current authenticated user
 */
function getCurrentUser(): Promise<User | null> {
  return new Promise((resolve) => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      unsubscribe();
      resolve(user);
    });
  });
}

/**
 * Ensure user is authenticated before performing operations
 */
async function ensureAuthenticated(): Promise<User> {
  const user = auth.currentUser || await getCurrentUser();
  if (!user) {
    throw new Error('User must be authenticated to perform this operation');
  }
  return user;
}

export class ProjectService {
  /**
   * Save a new project to Firestore
   */
  static async saveProject(userId: string, projectData: CreateProjectData): Promise<string> {
    try {
      // Ensure user is authenticated
      const user = await ensureAuthenticated();
      
      // Verify the userId matches the authenticated user
      if (user.uid !== userId) {
        throw new Error('User ID mismatch. Cannot save project for different user.');
      }

      const projectToSave = {
        ...projectData,
        userId: user.uid, // Use authenticated user's ID
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
      };

      console.log('Saving project for user:', user.uid);
      const docRef = await addDoc(collection(db, PROJECTS_COLLECTION), projectToSave);
      console.log('Project saved successfully with ID:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('Error saving project:', error);
      throw new Error(`Failed to save project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update an existing project
   */
  static async updateProject(projectId: string, updates: Partial<CreateProjectData>): Promise<void> {
    try {
      // Ensure user is authenticated
      await ensureAuthenticated();
      
      const projectRef = doc(db, PROJECTS_COLLECTION, projectId);
      await updateDoc(projectRef, {
        ...updates,
        updatedAt: Timestamp.now(),
      });
    } catch (error) {
      console.error('Error updating project:', error);
      throw new Error(`Failed to update project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get all projects for a specific user
   */
  static async getUserProjects(userId: string): Promise<SavedProject[]> {
    try {
      // Ensure user is authenticated
      const user = await ensureAuthenticated();
      
      // Verify the userId matches the authenticated user
      if (user.uid !== userId) {
        throw new Error('Cannot access projects for different user.');
      }

      const q = query(
        collection(db, PROJECTS_COLLECTION),
        where('userId', '==', user.uid),
        orderBy('updatedAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const projects: SavedProject[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        projects.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt.toDate(),
          updatedAt: data.updatedAt.toDate(),
        } as SavedProject);
      });

      return projects;
    } catch (error) {
      console.error('Error fetching user projects:', error);
      throw new Error(`Failed to fetch projects: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get a specific project by ID
   */
  static async getProject(projectId: string): Promise<SavedProject | null> {
    try {
      const projectRef = doc(db, PROJECTS_COLLECTION, projectId);
      const projectSnap = await getDoc(projectRef);

      if (!projectSnap.exists()) {
        return null;
      }

      const data = projectSnap.data();
      return {
        id: projectSnap.id,
        ...data,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
      } as SavedProject;
    } catch (error) {
      console.error('Error fetching project:', error);
      throw new Error('Failed to fetch project');
    }
  }

  /**
   * Delete a project
   */
  static async deleteProject(projectId: string): Promise<void> {
    try {
      // Ensure user is authenticated
      await ensureAuthenticated();
      
      const projectRef = doc(db, PROJECTS_COLLECTION, projectId);
      await deleteDoc(projectRef);
    } catch (error) {
      console.error('Error deleting project:', error);
      throw new Error(`Failed to delete project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update project title
   */
  static async updateProjectTitle(projectId: string, newTitle: string): Promise<void> {
    try {
      const projectRef = doc(db, PROJECTS_COLLECTION, projectId);
      await updateDoc(projectRef, {
        title: newTitle,
        updatedAt: Timestamp.now(),
      });
    } catch (error) {
      console.error('Error updating project title:', error);
      throw new Error('Failed to update project title');
    }
  }

  /**
   * Update project status
   */
  static async updateProjectStatus(projectId: string, status: SavedProject['status']): Promise<void> {
    try {
      const projectRef = doc(db, PROJECTS_COLLECTION, projectId);
      await updateDoc(projectRef, {
        status,
        updatedAt: Timestamp.now(),
      });
    } catch (error) {
      console.error('Error updating project status:', error);
      throw new Error('Failed to update project status');
    }
  }

  /**
   * Update generated chapters for a project
   */
  static async updateGeneratedChapters(projectId: string, generatedChapters: Record<string, boolean>): Promise<void> {
    try {
      const projectRef = doc(db, PROJECTS_COLLECTION, projectId);
      await updateDoc(projectRef, {
        generatedChapters,
        updatedAt: Timestamp.now(),
      });
    } catch (error) {
      console.error('Error updating generated chapters:', error);
      throw new Error('Failed to update generated chapters');
    }
  }

  /**
   * Save chapter content to subcollection
   */
  static async saveChapterContent(projectId: string, chapterData: CreateChapterData): Promise<void> {
    try {
      // Ensure user is authenticated
      await ensureAuthenticated();

      const chapterKey = `${chapterData.mainChapterTitle}-${chapterData.subChapterTitle}`;
      const chaptersRef = collection(db, PROJECTS_COLLECTION, projectId, CHAPTERS_SUBCOLLECTION);
      const chapterDocRef = doc(chaptersRef, chapterKey);

      const chapterToSave = {
        ...chapterData,
        id: chapterKey,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
      };

      await setDoc(chapterDocRef, chapterToSave);
    } catch (error) {
      console.error('Error saving chapter content:', error);
      throw new Error('Failed to save chapter content');
    }
  }

  /**
   * Update chapter content in subcollection
   */
  static async updateChapterContent(projectId: string, chapterKey: string, content: string, mainChapterTitle?: string, subChapterTitle?: string): Promise<void> {
    try {
      // Ensure user is authenticated
      await ensureAuthenticated();

      const chaptersRef = collection(db, PROJECTS_COLLECTION, projectId, CHAPTERS_SUBCOLLECTION);
      const chapterDocRef = doc(chaptersRef, chapterKey);

      // Check if document exists first
      const chapterDoc = await getDoc(chapterDocRef);

      if (chapterDoc.exists()) {
        // Update existing chapter
        await updateDoc(chapterDocRef, {
          content,
          updatedAt: Timestamp.now(),
        });
      } else {
        // Create new chapter document if it doesn't exist
        if (!mainChapterTitle || !subChapterTitle) {
          throw new Error('mainChapterTitle and subChapterTitle are required for new chapter creation');
        }

        const chapterToSave = {
          id: chapterKey,
          projectId,
          mainChapterTitle,
          subChapterTitle,
          content,
          chapterIndex: 0, // Will be updated by caller if needed
          subChapterIndex: 0, // Will be updated by caller if needed
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now(),
        };

        await setDoc(chapterDocRef, chapterToSave);
      }
    } catch (error) {
      console.error('Error updating chapter content:', error);
      throw new Error('Failed to update chapter content');
    }
  }

  /**
   * Get chapter content from subcollection
   */
  static async getChapterContent(projectId: string, chapterKey: string): Promise<ChapterDocument | null> {
    try {
      const chaptersRef = collection(db, PROJECTS_COLLECTION, projectId, CHAPTERS_SUBCOLLECTION);
      const chapterDocRef = doc(chaptersRef, chapterKey);
      const chapterSnap = await getDoc(chapterDocRef);

      if (!chapterSnap.exists()) {
        return null;
      }

      const data = chapterSnap.data();
      return {
        id: chapterSnap.id,
        ...data,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
      } as ChapterDocument;
    } catch (error) {
      console.error('Error fetching chapter content:', error);
      throw new Error('Failed to fetch chapter content');
    }
  }

  /**
   * Get all chapter contents for a project
   */
  static async getAllChapterContents(projectId: string): Promise<Record<string, string>> {
    try {
      const chaptersRef = collection(db, PROJECTS_COLLECTION, projectId, CHAPTERS_SUBCOLLECTION);
      const chaptersSnap = await getDocs(chaptersRef);

      const chapterContent: Record<string, string> = {};
      chaptersSnap.forEach((doc) => {
        const data = doc.data();
        chapterContent[doc.id] = data.content;
      });

      return chapterContent;
    } catch (error) {
      console.error('Error fetching all chapter contents:', error);
      throw new Error('Failed to fetch chapter contents');
    }
  }

  /**
   * Delete chapter content from subcollection
   */
  static async deleteChapterContent(projectId: string, chapterKey: string): Promise<void> {
    try {
      // Ensure user is authenticated
      await ensureAuthenticated();

      const chaptersRef = collection(db, PROJECTS_COLLECTION, projectId, CHAPTERS_SUBCOLLECTION);
      const chapterDocRef = doc(chaptersRef, chapterKey);
      await deleteDoc(chapterDocRef);
    } catch (error) {
      console.error('Error deleting chapter content:', error);
      throw new Error('Failed to delete chapter content');
    }
  }
}