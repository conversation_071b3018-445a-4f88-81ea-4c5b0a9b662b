import React, { createContext, useContext, useState, ReactNode } from 'react';
import { BookTitle, SavedProject } from '@shared/types';
import { LastGeneratedChapter } from '@/lib/projectUtils';

interface SavedIdeaData {
  selectedTitle: BookTitle;
  originalTopic: string;
  workflowStep: 'title-selection';
  fromSavedIdea: boolean;
  savedIdeaId: string;
}

interface AutoLoadChapterData {
  shouldAutoLoad: boolean;
  lastGeneratedChapter: LastGeneratedChapter | null;
}

interface AppDataContextType {
  // Saved idea workflow data
  savedIdeaData: SavedIdeaData | null;
  setSavedIdeaData: (data: SavedIdeaData | null) => void;

  // Project loading data
  projectToLoad: SavedProject | null;
  setProjectToLoad: (project: SavedProject | null) => void;

  // Auto-load chapter data
  autoLoadChapterData: AutoLoadChapterData | null;
  setAutoLoadChapterData: (data: AutoLoadChapterData | null) => void;

  // Current project ID for updates
  currentProjectId: string | null;
  setCurrentProjectId: (id: string | null) => void;

  // Chapter content storage
  chapterContent: Record<string, string>;
  setChapterContent: (key: string, content: string) => void;
  getChapterContent: (key: string) => string | null;
  removeChapterContent: (key: string) => void;
  clearAllChapterContent: () => void;

  // Saved idea deletion notification
  deletedIdeaId: string | null;
  setDeletedIdeaId: (id: string | null) => void;
}

const AppDataContext = createContext<AppDataContextType | undefined>(undefined);

export function AppDataProvider({ children }: { children: ReactNode }) {
  const [savedIdeaData, setSavedIdeaData] = useState<SavedIdeaData | null>(null);
  const [projectToLoad, setProjectToLoad] = useState<SavedProject | null>(null);
  const [autoLoadChapterData, setAutoLoadChapterData] = useState<AutoLoadChapterData | null>(null);
  const [currentProjectId, setCurrentProjectId] = useState<string | null>(null);
  const [chapterContent, setChapterContentState] = useState<Record<string, string>>({});
  const [deletedIdeaId, setDeletedIdeaId] = useState<string | null>(null);

  const setChapterContent = (key: string, content: string) => {
    setChapterContentState(prev => ({
      ...prev,
      [key]: content
    }));
  };

  const getChapterContent = (key: string): string | null => {
    return chapterContent[key] || null;
  };

  const removeChapterContent = (key: string) => {
    setChapterContentState(prev => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });
  };

  const clearAllChapterContent = () => {
    setChapterContentState({});
  };

  const value: AppDataContextType = {
    savedIdeaData,
    setSavedIdeaData,
    projectToLoad,
    setProjectToLoad,
    autoLoadChapterData,
    setAutoLoadChapterData,
    currentProjectId,
    setCurrentProjectId,
    chapterContent,
    setChapterContent,
    getChapterContent,
    removeChapterContent,
    clearAllChapterContent,
    deletedIdeaId,
    setDeletedIdeaId
  };

  return (
    <AppDataContext.Provider value={value}>
      {children}
    </AppDataContext.Provider>
  );
}

export function useAppData() {
  const context = useContext(AppDataContext);
  if (context === undefined) {
    throw new Error('useAppData must be used within an AppDataProvider');
  }
  return context;
}
