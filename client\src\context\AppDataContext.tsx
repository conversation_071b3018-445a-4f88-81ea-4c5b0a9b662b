import React, { createContext, useContext, useState, ReactNode } from 'react';
import { BookTitle, SavedProject } from '@shared/types';

interface SavedIdeaData {
  selectedTitle: BookTitle;
  originalTopic: string;
  workflowStep: 'title-selection';
  fromSavedIdea: boolean;
  savedIdeaId: string;
}

interface AppDataContextType {
  // Saved idea workflow data
  savedIdeaData: SavedIdeaData | null;
  setSavedIdeaData: (data: SavedIdeaData | null) => void;

  // Project loading data
  projectToLoad: SavedProject | null;
  setProjectToLoad: (project: SavedProject | null) => void;

  // Current project ID for updates
  currentProjectId: string | null;
  setCurrentProjectId: (id: string | null) => void;

  // Chapter content storage
  chapterContent: Record<string, string>;
  setChapterContent: (key: string, content: string) => void;
  getChapterContent: (key: string) => string | null;
  removeChapterContent: (key: string) => void;
  clearAllChapterContent: () => void;

  // Saved idea deletion notification
  deletedIdeaId: string | null;
  setDeletedIdeaId: (id: string | null) => void;
}

const AppDataContext = createContext<AppDataContextType | undefined>(undefined);

export function AppDataProvider({ children }: { children: ReactNode }) {
  const [savedIdeaData, setSavedIdeaData] = useState<SavedIdeaData | null>(null);
  const [projectToLoad, setProjectToLoad] = useState<SavedProject | null>(null);
  const [currentProjectId, setCurrentProjectId] = useState<string | null>(null);
  const [chapterContent, setChapterContentState] = useState<Record<string, string>>({});
  const [deletedIdeaId, setDeletedIdeaId] = useState<string | null>(null);

  const setChapterContent = (key: string, content: string) => {
    setChapterContentState(prev => ({
      ...prev,
      [key]: content
    }));
  };

  const getChapterContent = (key: string): string | null => {
    return chapterContent[key] || null;
  };

  const removeChapterContent = (key: string) => {
    setChapterContentState(prev => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });
  };

  const clearAllChapterContent = () => {
    setChapterContentState({});
  };

  const value: AppDataContextType = {
    savedIdeaData,
    setSavedIdeaData,
    projectToLoad,
    setProjectToLoad,
    currentProjectId,
    setCurrentProjectId,
    chapterContent,
    setChapterContent,
    getChapterContent,
    removeChapterContent,
    clearAllChapterContent,
    deletedIdeaId,
    setDeletedIdeaId
  };

  return (
    <AppDataContext.Provider value={value}>
      {children}
    </AppDataContext.Provider>
  );
}

export function useAppData() {
  const context = useContext(AppDataContext);
  if (context === undefined) {
    throw new Error('useAppData must be used within an AppDataProvider');
  }
  return context;
}
