import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import { ProjectService } from '@/lib/projectService';
import { SavedIdeasService } from '@/lib/savedIdeasService';

export interface DataCounts {
  currentProjects: number;
  savedIdeas: number;
  isLoading: boolean;
  error: string | null;
}

// Global state for count updates across components
let globalCountUpdateCallbacks: (() => void)[] = [];

/**
 * Trigger a global refresh of all data count hooks
 * 
 * Call this function from pages or components that modify project or idea data 
 * to ensure the navbar count badges are updated immediately.
 * 
 * @example
 * ```tsx
 * const handleSaveProject = async () => {
 *   await ProjectService.saveProject(...);
 *   refreshAllDataCounts(); // Update navbar badges
 * };
 * ```
 */
export function refreshAllDataCounts() {
  globalCountUpdateCallbacks.forEach(callback => callback());
}

/**
 * Custom hook to fetch and monitor data counts for Current Projects and Saved Ideas
 * Automatically refetches when authentication state changes and when data is modified
 */
export function useDataCounts(): DataCounts {
  const { user } = useAuth();
  const [counts, setCounts] = useState<DataCounts>({
    currentProjects: 0,
    savedIdeas: 0,
    isLoading: true,
    error: null,
  });

  const fetchCounts = useCallback(async () => {
    if (!user?.uid) {
      setCounts({
        currentProjects: 0,
        savedIdeas: 0,
        isLoading: false,
        error: null,
      });
      return;
    }

    setCounts(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Fetch both counts in parallel for better performance
      const [projects, ideas] = await Promise.all([
        ProjectService.getUserProjects(user.uid).catch(() => []), // Return empty array on error
        SavedIdeasService.getUserIdeas(user.uid).catch(() => []), // Return empty array on error
      ]);

      setCounts({
        currentProjects: projects.length,
        savedIdeas: ideas.length,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      console.error('Error fetching data counts:', error);
      setCounts(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to load counts',
      }));
    }
  }, [user?.uid]);

  // Register this hook for global updates
  useEffect(() => {
    globalCountUpdateCallbacks.push(fetchCounts);
    return () => {
      globalCountUpdateCallbacks = globalCountUpdateCallbacks.filter(cb => cb !== fetchCounts);
    };
  }, [fetchCounts]);

  // Fetch counts when user changes or component mounts
  useEffect(() => {
    fetchCounts();
  }, [fetchCounts]);

  // Refetch counts when the user navigates back to the app (visibility change)
  useEffect(() => {
    if (!user?.uid) return;

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        fetchCounts();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [user?.uid, fetchCounts]);

  return counts;
}

/**
 * Hook for manually refreshing data counts
 * Useful for triggering immediate updates after data changes
 */
export function useRefreshCounts() {
  const { user } = useAuth();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const refreshCounts = async (): Promise<DataCounts> => {
    if (!user?.uid) {
      return {
        currentProjects: 0,
        savedIdeas: 0,
        isLoading: false,
        error: null,
      };
    }

    setIsRefreshing(true);

    try {
      const [projects, ideas] = await Promise.all([
        ProjectService.getUserProjects(user.uid).catch(() => []),
        SavedIdeasService.getUserIdeas(user.uid).catch(() => []),
      ]);

      const counts = {
        currentProjects: projects.length,
        savedIdeas: ideas.length,
        isLoading: false,
        error: null,
      };

      setIsRefreshing(false);
      return counts;
    } catch (error) {
      console.error('Error refreshing data counts:', error);
      setIsRefreshing(false);
      throw error;
    }
  };

  return { refreshCounts, isRefreshing };
}