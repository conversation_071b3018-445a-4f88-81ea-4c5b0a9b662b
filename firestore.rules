rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Projects collection rules
    match /projects/{projectId} {
      // Allow users to read, write, update, and delete their own projects
      allow read, write, update, delete: if request.auth != null
        && request.auth.uid == resource.data.userId;

      // Allow users to create new projects for themselves
      allow create: if request.auth != null
        && request.auth.uid == request.resource.data.userId;

      // Chapters subcollection rules
      match /chapters/{chapterKey} {
        // Allow users to read, write, update, and delete chapters for their own projects
        allow read, write, update, delete: if request.auth != null
          && request.auth.uid == get(/databases/$(database)/documents/projects/$(projectId)).data.userId;

        // Allow users to create new chapters for their own projects
        allow create: if request.auth != null
          && request.auth.uid == get(/databases/$(database)/documents/projects/$(projectId)).data.userId;
      }
    }
    
    // Saved Ideas collection rules
    match /savedIdeas/{ideaId} {
      // Allow users to read, write, update, and delete their own saved ideas
      allow read, write, update, delete: if request.auth != null 
        && request.auth.uid == resource.data.userId;
      
      // Allow users to create new saved ideas for themselves
      allow create: if request.auth != null 
        && request.auth.uid == request.resource.data.userId;
    }
    
    // Default deny rule for all other documents
    match /{document=**} {
      allow read, write: if false;
    }
  }
}