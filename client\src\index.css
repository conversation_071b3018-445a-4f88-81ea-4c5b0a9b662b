@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Premium Glass Theme Color Variables - Enhanced for Modern Book Authoring */
    --background: 220 25% 4%; /* Deeper, richer background */
    --foreground: 220 15% 92%; /* Crisp, high-contrast text */
    --card: 220 20% 12%; /* Enhanced card background */
    --card-foreground: 220 15% 92%; /* Consistent high contrast */
    --popover: 220 20% 8%; /* Deeper popover background */
    --popover-foreground: 220 15% 92%; /* High contrast popover text */
    --primary: 220 15% 92%; /* Premium primary color */
    --primary-foreground: 220 25% 6%; /* Deep contrast for primary */
    --secondary: 220 20% 20%; /* Enhanced secondary background */
    --secondary-foreground: 220 15% 92%; /* High contrast secondary text */
    --muted: 220 20% 30%; /* Improved muted background */
    --muted-foreground: 220 15% 82%; /* Better contrast for muted text */
    --accent: 220 40% 68%; /* Slightly increased from 65% */
    --accent-foreground: 220 15% 90%; /* Increased from 85% for better contrast */
    --destructive: 0 65% 55%; /* Slightly increased for better contrast */
    --destructive-foreground: 220 15% 95%; /* Increased for better contrast */
    --border: 220 15% 35%; /* Increased from 30% for better visibility */
    --input: 220 15% 28%; /* Slightly increased from 25% */
    --ring: 220 40% 68%; /* Matches accent color */
    --chart-1: 220 70% 55%; /* Improved contrast for charts */
    --chart-2: 160 60% 50%; /* Improved contrast for charts */
    --chart-3: 30 80% 60%; /* Improved contrast for charts */
    --chart-4: 280 65% 65%; /* Improved contrast for charts */
    --chart-5: 340 75% 60%; /* Improved contrast for charts */
    --sidebar-background: 220 15% 12%;
    --sidebar-foreground: 220 15% 90%; /* Increased from 85% for better contrast */
    --sidebar-primary: 220 15% 90%; /* Increased from 85% for better contrast */
    --sidebar-primary-foreground: 220 20% 8%; /* Darkened from 10% for better contrast */
    --sidebar-accent: 220 40% 68%; /* Slightly increased from 65% */
    --sidebar-accent-foreground: 220 15% 90%; /* Increased from 85% for better contrast */
    --sidebar-border: 220 15% 35%; /* Increased from 30% for better visibility */
    --sidebar-ring: 220 40% 68%; /* Matches accent color */
    --radius: 0.75rem;

    /* Premium accessibility and visual enhancement variables */
    --disabled-foreground: 220 15% 65%; /* Better disabled text visibility */
    --disabled-background: 220 20% 15%; /* Enhanced disabled background */
    --placeholder-foreground: 220 15% 75%; /* Improved placeholder contrast */
    --icon-muted: 220 15% 78%; /* Better icon visibility */
    --glass-enhanced: rgba(255, 255, 255, 0.12); /* Premium glass effect */

    /* Premium gradient variables for nostalgic web aesthetics */
    --gradient-primary-start: 210 100% 56%; /* Vibrant blue start */
    --gradient-primary-end: 240 100% 70%; /* Purple-blue end */
    --gradient-secondary-start: 280 100% 70%; /* Purple start */
    --gradient-secondary-end: 320 100% 75%; /* Pink-purple end */
    --gradient-accent-start: 160 100% 45%; /* Teal start */
    --gradient-accent-end: 200 100% 55%; /* Blue-teal end */
    --gradient-warm-start: 45 100% 60%; /* Warm orange start */
    --gradient-warm-end: 25 100% 65%; /* Warm red-orange end */
    --gradient-success-start: 140 100% 45%; /* Green start */
    --gradient-success-end: 160 100% 50%; /* Blue-green end */
    --gradient-warning-start: 35 100% 55%; /* Orange start */
    --gradient-warning-end: 15 100% 60%; /* Red-orange end */
  }

  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    background: linear-gradient(135deg,
      hsl(220, 25%, 4%) 0%,
      hsl(230, 30%, 6%) 25%,
      hsl(240, 25%, 8%) 50%,
      hsl(220, 30%, 6%) 75%,
      hsl(210, 25%, 5%) 100%);
    min-height: 100vh;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "ss01" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    line-height: 1.6;
    letter-spacing: -0.01em;
  }

  /* Premium Typography Hierarchy */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    letter-spacing: -0.025em;
    color: hsl(var(--foreground));
  }

  h1 {
    font-size: clamp(2rem, 4vw, 3.5rem);
    font-weight: 700;
    letter-spacing: -0.04em;
    line-height: 1.1;
  }

  h2 {
    font-size: clamp(1.5rem, 3vw, 2.5rem);
    font-weight: 650;
    letter-spacing: -0.03em;
  }

  h3 {
    font-size: clamp(1.25rem, 2.5vw, 1.875rem);
    font-weight: 600;
    letter-spacing: -0.02em;
  }

  p {
    line-height: 1.7;
    letter-spacing: -0.005em;
    color: hsl(var(--foreground) / 0.9);
  }

  .text-muted-foreground {
    line-height: 1.6;
    letter-spacing: -0.005em;
  }

  /* Premium Focus States and Interactions */
  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent focus-visible:ring-offset-2;
  }

  /* Enhanced Scrollbar Styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    transition: background 0.3s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  /* Premium Animation Classes */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Premium Glass effect base styles - Enhanced for modern authoring */
  .glass {
    background: var(--glass-enhanced);
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow:
      0 8px 32px 0 rgba(31, 38, 135, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .glass-card {
    background: rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(12px) saturate(150%);
    -webkit-backdrop-filter: blur(12px) saturate(150%);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow:
      0 4px 20px 0 rgba(0, 0, 0, 0.35),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .glass-card:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.25);
    box-shadow:
      0 8px 32px 0 rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }

  .glass-nav {
    background: rgba(255, 255, 255, 0.1); /* Enhanced from 0.08 */
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2); /* Enhanced from 0.15 */
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.2);
  }

  /* Gradient Button Styles */
  .gradient-button {
    background: linear-gradient(to right, rgba(59, 130, 246, 0.9), rgba(79, 70, 229, 0.9));
    color: white;
    border: none;
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.4);
    transition: all 0.3s ease;
  }

  .gradient-button:hover {
    background: linear-gradient(to right, rgba(37, 99, 235, 1), rgba(67, 56, 202, 1));
    transform: translateY(-1px);
    box-shadow: 0 6px 20px 0 rgba(59, 130, 246, 0.5);
  }

  .gradient-button-secondary {
    background: linear-gradient(to right, rgba(168, 85, 247, 0.9), rgba(139, 92, 246, 0.9));
    color: white;
    border: none;
    box-shadow: 0 4px 14px 0 rgba(168, 85, 247, 0.4);
    transition: all 0.3s ease;
  }

  .gradient-button-secondary:hover {
    background: linear-gradient(to right, rgba(147, 51, 234, 1), rgba(126, 34, 206, 1));
    transform: translateY(-1px);
    box-shadow: 0 6px 20px 0 rgba(168, 85, 247, 0.5);
  }

  .gradient-button-destructive {
    background: linear-gradient(to right, rgba(239, 68, 68, 0.9), rgba(225, 29, 72, 0.9));
    color: white;
    border: none;
    box-shadow: 0 4px 14px 0 rgba(239, 68, 68, 0.4);
    transition: all 0.3s ease;
  }

  .gradient-button-destructive:hover {
    background: linear-gradient(to right, rgba(220, 38, 38, 1), rgba(190, 18, 60, 1));
    transform: translateY(-1px);
    box-shadow: 0 6px 20px 0 rgba(239, 68, 68, 0.5);
  }

  /* Legacy glass button styles - keeping for backward compatibility */
  .glass-button {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
  }

  .glass-button:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px 0 rgba(31, 38, 135, 0.4);
  }

  .glass-input {
    background: rgba(255, 255, 255, 0.08); /* Enhanced from 0.05 for better contrast */
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(6px);
    border: 1px solid rgba(255, 255, 255, 0.2); /* Enhanced from 0.15 */
    transition: all 0.3s ease;
  }

  .glass-input:focus {
    background: rgba(255, 255, 255, 0.12); /* Enhanced from 0.1 */
    border-color: rgba(255, 255, 255, 0.35); /* Enhanced from 0.3 */
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.15); /* Enhanced glow */
  }

  /* Premium Loading Animations */
  
  /* Hardware acceleration for all animated elements */
  .animate-glass-spin,
  .animate-shimmer,
  .animate-typewriter,
  .animate-pulse-glow,
  .animate-particle-float,
  .animate-ripple,
  .animate-scale-bounce {
    will-change: transform, opacity;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
  }

  /* Shimmer effect with better performance */
  .animate-shimmer {
    background-size: 200px 100%;
    background-repeat: no-repeat;
    animation: shimmer 2s infinite ease-in-out;
  }

  /* Glass spinner glow effect */
  .glass-spinner-glow {
    position: absolute;
    inset: -2px;
    border-radius: 50%;
    background: conic-gradient(from 0deg, transparent, rgba(59, 130, 246, 0.4), transparent);
    animation: glass-spin 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    z-index: -1;
  }

  /* Typewriter cursor */
  .typewriter-cursor {
    display: inline-block;
    width: 2px;
    height: 1em;
    background-color: currentColor;
    animation: blink 1s step-end infinite;
  }

  /* Typewriter fade-in effect */
  .typewriter-fade-in {
    animation: typewriter-fade-in 0.3s ease-in-out;
  }

  @keyframes typewriter-fade-in {
    from {
      opacity: 0;
      transform: translateY(5px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Loading text dots animation */
  .loading-dots {
    display: inline-flex;
    gap: 1px;
  }

  .loading-dots span {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: currentColor;
    animation: loading-dots 1.4s infinite ease-in-out;
  }

  .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
  .loading-dots span:nth-child(2) { animation-delay: -0.16s; }
  .loading-dots span:nth-child(3) { animation-delay: 0; }

  @keyframes loading-dots {
    0%, 80%, 100% {
      transform: scale(0);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Particle floating animation */
  .particle {
    position: absolute;
    border-radius: 50%;
    background: rgba(59, 130, 246, 0.6);
    pointer-events: none;
    z-index: 1;
  }

  /* Content writing animation */
  .writing-animation {
    position: relative;
    overflow: hidden;
  }

  .writing-animation::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 2px;
    height: 100%;
    background: linear-gradient(to bottom, transparent, rgba(59, 130, 246, 0.8), transparent);
    animation: writing-cursor 1.5s ease-in-out infinite;
  }

  @keyframes writing-cursor {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
  }

  /* Reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .animate-glass-spin,
    .animate-shimmer,
    .animate-typewriter,
    .animate-pulse-glow,
    .animate-particle-float,
    .animate-ripple,
    .animate-scale-bounce,
    .loading-dots span,
    .writing-animation::after {
      animation: none !important;
    }

    .glass-spinner-glow {
      display: none;
    }

    /* Provide static alternatives */
    .animate-shimmer {
      opacity: 0.7;
    }

    .typewriter-cursor {
      opacity: 0.5;
    }

    .loading-dots span {
      opacity: 0.7;
      transform: scale(0.8);
    }
  }

  /* Focus styles for accessibility - Enhanced */
  .loading-button:focus-visible {
    outline: 2px solid rgba(59, 130, 246, 0.8);
    outline-offset: 2px;
  }
  
  /* Enhanced focus styles for better accessibility */
  button:focus-visible,
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible,
  [role="button"]:focus-visible,
  [role="textbox"]:focus-visible {
    outline: 2px solid hsl(var(--accent));
    outline-offset: 2px;
    transition: outline-color 0.2s ease;
  }
  
  /* Skip link for keyboard navigation */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 9999;
    font-weight: 600;
    transition: top 0.3s;
  }
  
  .skip-link:focus {
    top: 6px;
  }

  /* High contrast mode support - Enhanced */
  @media (prefers-contrast: high) {
    :root {
      /* Enhanced contrast colors for high contrast mode */
      --foreground: 220 15% 95%;
      --muted-foreground: 220 15% 85%;
      --border: 220 15% 50%;
      --disabled-foreground: 220 15% 70%;
      --icon-muted: 220 15% 85%;
    }
    
    .glass,
    .glass-card {
      border-width: 2px;
      border-color: hsl(var(--border));
      background: rgba(255, 255, 255, 0.15); /* More opaque for high contrast */
    }

    .animate-shimmer {
      background: linear-gradient(90deg, transparent 0px, hsl(var(--foreground)) 40px, transparent 80px);
      opacity: 0.3;
    }
    
    /* Ensure all text meets high contrast requirements */
    .text-muted-foreground {
      color: hsl(var(--muted-foreground)) !important;
    }
  }
  
  /* Accessibility utility classes */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
  
  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent focus-visible:ring-offset-2;
  }
  
  /* Enhanced button accessibility */
  .btn-accessible {
    @apply focus-ring transition-all duration-200;
  }
  
  .btn-accessible:disabled {
    @apply cursor-not-allowed;
    color: hsl(var(--disabled-foreground));
    background-color: hsl(var(--disabled-background));
  }
  
  /* Text contrast helpers */
  .text-high-contrast {
    color: hsl(var(--foreground));
    font-weight: 500;
  }
  
  .text-accessible-muted {
    color: hsl(var(--muted-foreground));
  }
  
  .text-accessible-placeholder {
    color: hsl(var(--placeholder-foreground));
  }
  
  /* Interactive element enhancements */
  .interactive-hover:hover {
    background-color: hsl(var(--accent) / 0.1);
    transition: background-color 0.2s ease;
  }
  
  .interactive-focus:focus-visible {
    box-shadow: 0 0 0 2px hsl(var(--accent));
    border-color: hsl(var(--accent));
  }
}