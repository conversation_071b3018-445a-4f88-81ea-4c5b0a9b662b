import { GoogleGenerativeAI } from "@google/generative-ai";

export interface ApiKeyConfig {
  key: string;
  isActive: boolean;
  lastError?: string;
  lastErrorTime?: number;
  quotaResetTime?: number;
}

export class ApiKeyManager {
  private apiKeys: ApiKeyConfig[] = [];
  private currentKeyIndex = 0;
  private readonly QUOTA_RESET_DELAY = 24 * 60 * 60 * 1000; // 24 hours
  private readonly ERROR_COOLDOWN = 5 * 60 * 1000; // 5 minutes

  constructor(apiKeysString?: string) {
    this.initializeKeys(apiKeysString);
  }

  private initializeKeys(apiKeysString?: string) {
    // Get API keys from environment or parameter
    const keysString = apiKeysString || process.env.GEMINI_API_KEYS || process.env.GEMINI_API_KEY;
    
    if (!keysString) {
      throw new Error("No Gemini API keys found. Please set GEMINI_API_KEY or GEMINI_API_KEYS environment variable.");
    }

    // Split by comma and clean up
    const keys = keysString.split(',').map(key => key.trim()).filter(key => key.length > 0);
    
    if (keys.length === 0) {
      throw new Error("No valid Gemini API keys found.");
    }

    this.apiKeys = keys.map(key => ({
      key,
      isActive: true,
      lastError: undefined,
      lastErrorTime: undefined,
      quotaResetTime: undefined
    }));

    console.log(`Initialized API key manager with ${this.apiKeys.length} keys`);
  }

  /**
   * Get the current active API key
   */
  getCurrentKey(): string {
    // Clean up expired cooldowns
    this.cleanupExpiredCooldowns();

    // Find the next active key
    const activeKeys = this.apiKeys.filter(config => config.isActive);
    
    if (activeKeys.length === 0) {
      // All keys are in cooldown, reset all and use the first one
      console.warn("All API keys are in cooldown, resetting all keys");
      this.resetAllKeys();
      return this.apiKeys[0].key;
    }

    // Rotate to next active key if current is inactive
    while (!this.apiKeys[this.currentKeyIndex].isActive) {
      this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;
    }

    return this.apiKeys[this.currentKeyIndex].key;
  }

  /**
   * Create a GoogleGenerativeAI instance with the current key
   */
  createGenerativeAI(): GoogleGenerativeAI {
    const apiKey = this.getCurrentKey();
    return new GoogleGenerativeAI(apiKey);
  }

  /**
   * Mark current key as having an error and rotate to next
   */
  markCurrentKeyError(error: any): void {
    const currentConfig = this.apiKeys[this.currentKeyIndex];
    const now = Date.now();

    if (this.isQuotaError(error)) {
      console.warn(`API key quota exceeded, marking key as inactive until tomorrow`);
      currentConfig.isActive = false;
      currentConfig.lastError = "Quota exceeded";
      currentConfig.lastErrorTime = now;
      currentConfig.quotaResetTime = now + this.QUOTA_RESET_DELAY;
    } else if (this.isRateLimitError(error)) {
      console.warn(`API key rate limited, marking key as inactive for 5 minutes`);
      currentConfig.isActive = false;
      currentConfig.lastError = "Rate limited";
      currentConfig.lastErrorTime = now;
      currentConfig.quotaResetTime = now + this.ERROR_COOLDOWN;
    } else {
      console.warn(`API key error: ${error.message || error}`);
      currentConfig.lastError = error.message || "Unknown error";
      currentConfig.lastErrorTime = now;
    }

    // Rotate to next key
    this.rotateToNextKey();
  }

  /**
   * Check if error is a quota/billing error
   */
  private isQuotaError(error: any): boolean {
    if (!error) return false;
    
    const errorString = error.toString().toLowerCase();
    const message = error.message?.toLowerCase() || '';
    
    return (
      error.status === 429 ||
      errorString.includes('quota') ||
      errorString.includes('billing') ||
      errorString.includes('exceeded your current quota') ||
      message.includes('quota') ||
      message.includes('billing')
    );
  }

  /**
   * Check if error is a rate limit error
   */
  private isRateLimitError(error: any): boolean {
    if (!error) return false;
    
    const errorString = error.toString().toLowerCase();
    const message = error.message?.toLowerCase() || '';
    
    return (
      error.status === 429 ||
      errorString.includes('rate limit') ||
      errorString.includes('too many requests') ||
      message.includes('rate limit') ||
      message.includes('too many requests')
    );
  }

  /**
   * Rotate to the next available key
   */
  private rotateToNextKey(): void {
    const startIndex = this.currentKeyIndex;
    
    do {
      this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;
    } while (
      !this.apiKeys[this.currentKeyIndex].isActive && 
      this.currentKeyIndex !== startIndex
    );
  }

  /**
   * Clean up expired cooldowns
   */
  private cleanupExpiredCooldowns(): void {
    const now = Date.now();
    
    this.apiKeys.forEach(config => {
      if (!config.isActive && config.quotaResetTime && now > config.quotaResetTime) {
        console.log(`Reactivating API key after cooldown period`);
        config.isActive = true;
        config.lastError = undefined;
        config.lastErrorTime = undefined;
        config.quotaResetTime = undefined;
      }
    });
  }

  /**
   * Reset all keys to active state (emergency fallback)
   */
  private resetAllKeys(): void {
    this.apiKeys.forEach(config => {
      config.isActive = true;
      config.lastError = undefined;
      config.lastErrorTime = undefined;
      config.quotaResetTime = undefined;
    });
    this.currentKeyIndex = 0;
  }

  /**
   * Get status of all keys
   */
  getStatus(): { totalKeys: number; activeKeys: number; keyStatuses: ApiKeyConfig[] } {
    this.cleanupExpiredCooldowns();
    
    return {
      totalKeys: this.apiKeys.length,
      activeKeys: this.apiKeys.filter(config => config.isActive).length,
      keyStatuses: this.apiKeys.map(config => ({
        ...config,
        key: config.key.substring(0, 10) + '...' // Mask the key for security
      }))
    };
  }

  /**
   * Retry logic with exponential backoff
   */
  async withRetry<T>(
    operation: (genAI: GoogleGenerativeAI) => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const genAI = this.createGenerativeAI();
        return await operation(genAI);
      } catch (error) {
        lastError = error;
        console.error(`Attempt ${attempt + 1} failed:`, error.message || error);

        // Mark current key as having an error
        this.markCurrentKeyError(error);

        // If this is the last attempt, don't wait
        if (attempt === maxRetries - 1) {
          break;
        }

        // Exponential backoff
        const delay = baseDelay * Math.pow(2, attempt);
        console.log(`Retrying in ${delay}ms with next API key...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }
}

// Global instance for server-side usage
let globalApiKeyManager: ApiKeyManager | null = null;

export function getApiKeyManager(): ApiKeyManager {
  if (!globalApiKeyManager) {
    globalApiKeyManager = new ApiKeyManager();
  }
  return globalApiKeyManager;
}

// Export for Netlify functions (they need to create their own instance)
export function createApiKeyManager(apiKeysString?: string): ApiKeyManager {
  return new ApiKeyManager(apiKeysString);
}
