import React from 'react';
import { BookSize, OutlineGenerationParams } from '@shared/types';
import { getBookSizeLabel, getBookSizeConstraints } from '@shared/bookSizeUtils';

interface DynamicOutlineDisplayProps {
  outlineParams: OutlineGenerationParams;
  selectedSize: BookSize;
  isAppliedFromTitle?: boolean;
  appliedTitleName?: string;
}

export default function DynamicOutlineDisplay({
  outlineParams,
  selectedSize,
  isAppliedFromTitle = false,
  appliedTitleName
}: DynamicOutlineDisplayProps) {
  const sizeConstraints = getBookSizeConstraints(selectedSize);

  // Calculate total sub-chapters based on dynamic counts or uniform distribution
  const totalSubChapters = outlineParams.subChapterCounts
    ? outlineParams.subChapterCounts.reduce((sum, count) => sum + count, 0)
    : outlineParams.maxChapters * outlineParams.maxSubChapters;

  return (
    <div className="p-4 bg-card/30 backdrop-blur-sm border border-border/50 rounded-lg">
      <div className="flex items-center mb-3">
        <svg className="h-5 w-5 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M4 5h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1z" />
          <path d="M4 9h16" />
          <path d="M4 14h16" />
          <path d="M4 19h16" />
        </svg>
        <h3 className="font-medium text-foreground">Dynamic Outline Structure</h3>
      </div>

      {/* Size Information */}
      <div className="mb-4 p-3 bg-muted/20 rounded-md">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-muted-foreground">Book Size:</span>
          <span className="text-sm font-semibold text-primary">{getBookSizeLabel(selectedSize)}</span>
        </div>
        <div className="text-xs text-muted-foreground">
          Constraints: {sizeConstraints.minChapters}-{sizeConstraints.maxChapters} chapters, {sizeConstraints.minSubChapters}-{sizeConstraints.maxSubChapters} total sub-chapters
        </div>
      </div>

      {/* Current Structure */}
      <div className="space-y-3">
        <div className="flex items-center justify-between p-3 bg-background/50 rounded-md">
          <div className="flex items-center">
            <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
            <span className="text-sm font-medium">Chapters</span>
          </div>
          <span className="text-lg font-bold text-primary">{outlineParams.maxChapters}</span>
        </div>

        <div className="flex items-center justify-between p-3 bg-background/50 rounded-md">
          <div className="flex items-center">
            <div className="w-2 h-2 bg-secondary rounded-full mr-3"></div>
            <span className="text-sm font-medium">
              {outlineParams.subChapterCounts ? "Sub-chapters Distribution" : "Sub-chapters per Chapter"}
            </span>
          </div>
          <span className="text-lg font-bold text-secondary">
            {outlineParams.subChapterCounts
              ? `(${outlineParams.subChapterCounts.join(', ')})`
              : outlineParams.maxSubChapters
            }
          </span>
        </div>

        <div className="flex items-center justify-between p-3 bg-background/50 rounded-md border-l-4 border-l-accent">
          <div className="flex items-center">
            <div className="w-2 h-2 bg-accent rounded-full mr-3"></div>
            <span className="text-sm font-medium">Total Sub-chapters</span>
          </div>
          <span className="text-lg font-bold text-accent">{totalSubChapters}</span>
        </div>
      </div>

      {/* Applied From Title Info */}
      {isAppliedFromTitle && appliedTitleName && (
        <div className="mt-4 p-3 bg-primary/10 border border-primary/20 rounded-md">
          <div className="flex items-center text-sm">
            <svg className="w-4 h-4 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span className="text-primary font-medium">Applied from:</span>
            <span className="text-primary/80 ml-1 truncate">"{appliedTitleName}"</span>
          </div>
          <div className="text-xs text-primary/70 mt-1">
            Structure adjusted to fit {getBookSizeLabel(selectedSize)} book size constraints
          </div>
        </div>
      )}

      {/* Validation Status */}
      <div className="mt-4">
        {totalSubChapters >= sizeConstraints.minSubChapters && totalSubChapters <= sizeConstraints.maxSubChapters ? (
          <div className="flex items-center text-sm text-green-600">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Structure fits within {getBookSizeLabel(selectedSize)} book size constraints
          </div>
        ) : (
          <div className="flex items-center text-sm text-amber-600">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            Structure will be adjusted to fit {getBookSizeLabel(selectedSize)} constraints during generation
          </div>
        )}
      </div>
    </div>
  );
}
