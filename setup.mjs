#!/usr/bin/env node

/**
 * Setup script for NonFictionBookBuilder development environment
 * This script helps set up the environment variables and dependencies
 */

import { existsSync, copyFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 Setting up NonFictionBookBuilder development environment...\n');

// Check if .env file exists
const envPath = join(__dirname, '.env');
const envExamplePath = join(__dirname, '.env.example');

if (!existsSync(envPath)) {
  if (existsSync(envExamplePath)) {
    console.log('📝 Creating .env file from .env.example...');
    copyFileSync(envExamplePath, envPath);
    console.log('✅ .env file created successfully!\n');
  } else {
    console.log('⚠️  No .env.example file found. Please create .env manually.\n');
  }
} else {
  console.log('✅ .env file already exists.\n');
}

// Instructions
console.log('📋 Setup Instructions:');
console.log('1. Update your .env file with your actual API keys');
console.log('2. Run "npm install" to install dependencies');
console.log('3. Run "npm run dev" to start the development server');
console.log('4. Open http://localhost:5000 in your browser\n');

console.log('🔑 Required Environment Variables:');
console.log('- GEMINI_API_KEY: Your Google Gemini AI API key\n');

console.log('📚 Documentation:');
console.log('- See .env.example for all available environment variables');
console.log('- Check README.md for detailed setup instructions\n');

console.log('🎉 Setup complete! Happy coding!');