import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-semibold ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:text-disabled-foreground disabled:bg-disabled-background relative overflow-hidden",
  {
    variants: {
      variant: {
        default: "bg-gradient-primary text-white hover:bg-gradient-primary-hover shadow-lg hover:shadow-xl hover:translate-y-[-2px] border border-transparent disabled:from-disabled-background disabled:to-disabled-background disabled:text-disabled-foreground before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/20 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300",
        destructive:
          "bg-gradient-destructive text-white hover:bg-gradient-destructive-hover shadow-lg hover:shadow-xl hover:translate-y-[-2px] border border-transparent disabled:from-disabled-background disabled:to-disabled-background disabled:text-disabled-foreground before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/20 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300",
        outline:
          "border-2 border-glass bg-glass-card backdrop-blur-sm text-white hover:bg-gradient-accent hover:border-transparent hover:text-white shadow-glass hover:shadow-lg hover:translate-y-[-1px] disabled:text-disabled-foreground disabled:border-disabled-foreground transition-all duration-300",
        secondary:
          "bg-gradient-secondary text-white hover:bg-gradient-secondary-hover shadow-lg hover:shadow-xl hover:translate-y-[-2px] border border-transparent disabled:from-disabled-background disabled:to-disabled-background disabled:text-disabled-foreground before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/20 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300",
        ghost: "text-white hover:bg-gradient-to-r hover:from-white/10 hover:to-white/5 hover:text-white disabled:text-disabled-foreground transition-all duration-200",
        link: "text-white font-medium underline-offset-4 hover:underline hover:text-blue-300 disabled:text-disabled-foreground disabled:no-underline transition-colors duration-200",
        // Premium variants for Generate All Sub-Chapters button states
        generating: "bg-gradient-warning text-white shadow-lg border border-transparent backdrop-blur-sm transition-all duration-300 ease-in-out disabled:from-disabled-background disabled:to-disabled-background disabled:text-disabled-foreground hover:bg-gradient-warning-hover hover:shadow-xl hover:translate-y-[-1px] before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/15 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300",
        generated: "bg-gradient-success text-white shadow-lg border border-transparent backdrop-blur-sm transition-all duration-300 ease-in-out disabled:from-disabled-background disabled:to-disabled-background disabled:text-disabled-foreground hover:bg-gradient-success-hover hover:shadow-xl hover:translate-y-[-1px] before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/15 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300",
        // New variant for Generated All Sub-Chapters button with improved contrast
        generatedSuccess: "bg-[#065f46] text-white shadow-lg border border-transparent backdrop-blur-sm transition-all duration-300 ease-in-out disabled:from-disabled-background disabled:to-disabled-background disabled:text-disabled-foreground hover:bg-[#047857] hover:shadow-xl hover:translate-y-[-1px] before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/15 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300",
        // New variant for Generating Sub-Chapters button with improved contrast (dark blue background with green text)
        generatingSubChapters: "bg-blue-900 text-green-400 shadow-lg border border-blue-700 backdrop-blur-sm transition-all duration-300 ease-in-out disabled:from-disabled-background disabled:to-disabled-background disabled:text-disabled-foreground hover:bg-blue-800 hover:shadow-xl hover:translate-y-[-1px] before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/15 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300",
        error: "bg-gradient-warm text-white shadow-lg border border-transparent backdrop-blur-sm transition-all duration-300 ease-in-out disabled:from-disabled-background disabled:to-disabled-background disabled:text-disabled-foreground hover:bg-gradient-warm-hover hover:shadow-xl hover:translate-y-[-1px] before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/15 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-6 py-3 text-base",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }