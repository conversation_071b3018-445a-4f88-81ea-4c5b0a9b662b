#!/usr/bin/env node

/**
 * Sync Environment Variables to Netlify
 * 
 * This script reads the .env file and syncs all environment variables to Netlify
 * 
 * Usage: node sync-env-to-netlify.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔄 Syncing environment variables from .env file to Netlify...\n');

// Function to parse .env file
function parseEnvFile(filePath) {
  const envVars = {};
  
  if (!fs.existsSync(filePath)) {
    console.error('❌ .env file not found at:', filePath);
    process.exit(1);
  }
  
  const envContent = fs.readFileSync(filePath, 'utf8');
  const lines = envContent.split('\n');
  
  for (const line of lines) {
    const trimmedLine = line.trim();
    if (trimmedLine && !trimmedLine.startsWith('#')) {
      const [key, ...valueParts] = trimmedLine.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim();
        // Remove quotes if present
        const cleanValue = value.replace(/^["']|["']$/g, '');
        envVars[key.trim()] = cleanValue;
      }
    }
  }
  
  return envVars;
}

// Main sync function
function syncEnvVars() {
  // 1. Parse .env file
  const envFilePath = path.join(__dirname, '.env');
  console.log('📖 Reading environment variables from .env file...');
  const envVars = parseEnvFile(envFilePath);
  
  console.log('📝 Found environment variables:');
  Object.keys(envVars).forEach(key => {
    const maskedValue = key.toLowerCase().includes('key') || key.toLowerCase().includes('secret')
      ? '***MASKED***'
      : envVars[key];
    console.log(`   ${key}: ${maskedValue}`);
  });
  console.log('');

  // 2. Check if Netlify CLI is installed
  try {
    execSync('netlify --version', { encoding: 'utf8', stdio: 'pipe' });
    console.log('✅ Netlify CLI is available\n');
  } catch (error) {
    console.error('❌ Netlify CLI is not installed. Please install it with: npm install -g netlify-cli');
    process.exit(1);
  }

  // 3. Check if we're linked to a Netlify site
  try {
    execSync('netlify status', { encoding: 'utf8', stdio: 'pipe' });
    console.log('✅ Netlify site is linked\n');
  } catch (error) {
    console.error('❌ Not linked to a Netlify site. Please run "netlify link" first.');
    process.exit(1);
  }

  // 4. Set environment variables in Netlify
  console.log('🔧 Setting environment variables in Netlify...');
  let successCount = 0;
  let errorCount = 0;
  
  for (const [key, value] of Object.entries(envVars)) {
    try {
      execSync(`netlify env:set "${key}" "${value}"`, { encoding: 'utf8', stdio: 'pipe' });
      console.log(`   ✅ Set ${key}`);
      successCount++;
    } catch (error) {
      console.error(`   ❌ Failed to set ${key}:`, error.message);
      errorCount++;
    }
  }
  
  console.log('');
  console.log(`📊 Summary: ${successCount} variables set successfully, ${errorCount} errors`);
  
  if (errorCount === 0) {
    console.log('🎉 All environment variables synced successfully!');
    console.log('💡 You can now deploy your site with: npm run deploy:netlify');
  } else {
    console.log('⚠️  Some environment variables failed to sync. Please check the errors above.');
    process.exit(1);
  }
}

// Run sync
try {
  syncEnvVars();
} catch (error) {
  console.error('💥 Sync failed:', error.message);
  process.exit(1);
}