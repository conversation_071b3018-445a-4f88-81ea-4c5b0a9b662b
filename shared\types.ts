export interface Chapter {
  title: string;
  subchapters: string[];
}

export interface BookOutline {
  chapters: Chapter[];
}

export interface GeneratedContent {
  mainChapterTitle: string;
  subChapterTitle: string;
  content: string;
  index: number;
}

export type WritingTone = 'professional' | 'casual' | 'academic' | 'conversational' | 'instructional';
export type WritingStyle = 'descriptive' | 'analytical' | 'persuasive' | 'narrative' | 'technical';
export type WritingLanguage = 'simple' | 'intermediate' | 'advanced' | 'technical';
export type TargetAudience = string;

// Book size types
export type BookSize = 'small' | 'medium' | 'large';

export interface BookSizeConstraints {
  minChapters: number;
  maxChapters: number;
  minSubChapters: number;
  maxSubChapters: number;
}

// Book language type for target language selection
export type BookLanguage =
  | 'ar' | 'bn' | 'bg' | 'zh-CN' | 'zh-TW' | 'hr' | 'cs' | 'da' | 'nl'
  | 'en' | 'et' | 'fi' | 'fr' | 'de' | 'el' | 'he' | 'hi'
  | 'hu' | 'id' | 'it' | 'ja' | 'ko' | 'lv' | 'lt' | 'no'
  | 'pl' | 'pt' | 'ro' | 'ru' | 'sr' | 'sk' | 'sl' | 'es'
  | 'sw' | 'sv' | 'th' | 'tr' | 'uk' | 'vi';

export interface LanguageOption {
  code: BookLanguage;
  name: string;
  isRTL?: boolean;
}

export interface OutlineGenerationParams {
  maxChapters: number;
  maxSubChapters: number;
  subChapterCounts?: number[]; // Array of specific sub-chapter counts per chapter (for dynamic distribution)
  tone?: WritingTone;
  style?: WritingStyle;
  language?: WritingLanguage;
  targetLanguage?: BookLanguage;
  targetAudience?: TargetAudience;
  bookSize?: BookSize;
  summary?: string; // Book summary for contextual guidance
}

export interface ContentGenerationParams {
  tone: WritingTone;
  style: WritingStyle;
  language: WritingLanguage;
  targetLanguage?: BookLanguage;
  targetAudience?: TargetAudience;
}

// New types for two-step workflow
export interface BookTitle {
  id: string;
  title: string;
  description?: string;
  // Enhanced parameters for comprehensive book generation
  chapterCount: number;
  subChapterCounts: number[]; // Array showing sub-chapters for each main chapter
  tone: WritingTone;
  style: WritingStyle;
  languageLevel: WritingLanguage;
  targetLanguage?: BookLanguage; // Added target language support
  targetAudience: TargetAudience;
  summary: string; // 2-3 line book summary
}

export interface BookTitlesResponse {
  titles: BookTitle[];
}

export interface TitleSelectionState {
  generatedTitles: BookTitle[];
  selectedTitle: BookTitle | null;
  isGeneratingTitles: boolean;
  titlesError: string | null;
}

// Project Persistence Types
export type ProjectStatus = 'outline_generated' | 'content_in_progress' | 'completed';

export interface SavedProject {
  id: string;
  userId: string;
  title: string;
  topic: string;
  outline: BookOutline;
  generationParams: OutlineGenerationParams;
  selectedTitle?: BookTitle;
  writingParams: {
    tone: WritingTone;
    style: WritingStyle;
    language: WritingLanguage;
    targetLanguage: BookLanguage;
    targetAudience: TargetAudience;
  };
  status: ProjectStatus;
  createdAt: Date;
  updatedAt: Date;
  generatedChapters?: Record<string, boolean>;
  bookSummary?: string;
}

export interface CreateProjectData {
  title: string;
  topic: string;
  outline: BookOutline;
  generationParams: OutlineGenerationParams;
  selectedTitle?: BookTitle;
  writingParams: {
    tone: WritingTone;
    style: WritingStyle;
    language: WritingLanguage;
    targetLanguage: BookLanguage;
    targetAudience: TargetAudience;
  };
  status: ProjectStatus;
  generatedChapters?: Record<string, boolean>;
  bookSummary?: string;
}

// Chapter Document for Subcollection Storage
export interface ChapterDocument {
  id: string; // chapterKey (mainChapter-subChapter)
  projectId: string;
  mainChapterTitle: string;
  subChapterTitle: string;
  content: string;
  chapterIndex: number;
  subChapterIndex: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateChapterData {
  projectId: string;
  mainChapterTitle: string;
  subChapterTitle: string;
  content: string;
  chapterIndex: number;
  subChapterIndex: number;
}

// Saved Ideas Types
export interface SavedIdea {
  id: string;
  userId: string;
  bookTitle: BookTitle; // Complete book title data with all metadata
  originalTopic: string; // Original user input that generated this title
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateSavedIdeaData {
  bookTitle: BookTitle;
  originalTopic: string;
}
