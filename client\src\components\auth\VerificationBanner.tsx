import { useState } from "react";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";

export default function VerificationBanner() {
  const { user, sendVerification } = useAuth();
  const [status, setStatus] = useState<"idle" | "sent" | "error" | "loading">("idle");

  if (!user || user.emailVerified) return null;

  const handleResend = async () => {
    setStatus("loading");
    try {
      await sendVerification();
      setStatus("sent");
    } catch {
      setStatus("error");
    }
  };

  return (
    <div className="glass-card border border-glass px-4 py-3 text-sm flex items-center justify-between">
      <div>
        Your email is not verified. Please verify to unlock all features.
        {status === "sent" && <span className="ml-2 text-green-400">Verification email sent.</span>}
        {status === "error" && <span className="ml-2 text-red-400">Failed to send verification.</span>}
      </div>
      <Button variant="outline" onClick={handleResend} disabled={status === "loading"}>
        {status === "loading" ? "Sending…" : "Resend"}
      </Button>
    </div>
  );
}

