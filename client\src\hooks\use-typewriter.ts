import { useState, useEffect, useRef, useCallback } from 'react';

interface UseTypewriterProps {
  text: string | string[];
  speed?: number;
  delay?: number;
  loop?: boolean;
  initialDelay?: number;
}

export const useTypewriter = ({
  text,
  speed = 50,
  delay = 1000,
  loop = true,
  initialDelay = 500
}: UseTypewriterProps) => {
  const [displayText, setDisplayText] = useState('');
  const [showCursor, setShowCursor] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [isActive, setIsActive] = useState(false);
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Convert text to array if it's a string
  const textArray = Array.isArray(text) ? text : [text];
  const currentText = textArray[currentTextIndex];

  // Function to reset the typewriter
  const resetTypewriter = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setDisplayText('');
    setIsDeleting(false);
    setIsPaused(false);
  }, []);

  // Function to pause the typewriter
  const pauseTypewriter = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsActive(false);
  }, []);

  // Function to resume the typewriter
  const resumeTypewriter = useCallback(() => {
    // Add a small delay before starting the animation
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      setIsActive(true);
    }, initialDelay);
  }, [initialDelay]);

  useEffect(() => {
    // Start with a delay
    timeoutRef.current = setTimeout(() => {
      setIsActive(true);
    }, initialDelay);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [initialDelay]);

  useEffect(() => {
    if (!isActive) {
      return;
    }

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    const type = () => {
      if (isDeleting) {
        // Deleting text
        if (displayText.length > 0) {
          setDisplayText(prev => prev.slice(0, -1));
          timeoutRef.current = setTimeout(type, speed / 2);
        } else {
          // Finished deleting, move to next text if array
          setIsDeleting(false);
          setIsPaused(true);

          // Move to next text in array
          if (textArray.length > 1) {
            setCurrentTextIndex(prev => (prev + 1) % textArray.length);
          }

          timeoutRef.current = setTimeout(() => {
            setIsPaused(false);
            if (loop) {
              type();
            }
          }, delay);
        }
      } else {
        // Typing text
        if (displayText.length < currentText.length) {
          setDisplayText(prev => currentText.slice(0, prev.length + 1));
          timeoutRef.current = setTimeout(type, speed);
        } else {
          // Finished typing, pause before deleting
          setIsPaused(true);
          timeoutRef.current = setTimeout(() => {
            setIsPaused(false);
            if (loop) {
              setIsDeleting(true);
              type();
            }
          }, delay);
        }
      }
    };

    // Start the typing effect
    timeoutRef.current = setTimeout(type, speed);

    // Clean up on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [currentText, speed, delay, loop, displayText, isDeleting, isPaused, isActive, textArray.length]);

  // Blinking cursor effect
  useEffect(() => {
    const cursorInterval = setInterval(() => {
      setShowCursor(prev => !prev);
    }, 500);

    return () => clearInterval(cursorInterval);
  }, []);

  return { 
    displayText, 
    showCursor, 
    resetTypewriter, 
    pauseTypewriter, 
    resumeTypewriter 
  };
};