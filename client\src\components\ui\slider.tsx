import * as React from "react"
import * as SliderPrimitive from "@radix-ui/react-slider"

import { cn } from "@/lib/utils"

const Slider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>
>(({ className, ...props }, ref) => (
  <SliderPrimitive.Root
    ref={ref}
    className={cn(
      "relative flex w-full touch-none select-none items-center h-6",
      className
    )}
    {...props}
  >
    <SliderPrimitive.Track className="relative h-3 w-full grow overflow-hidden rounded-full bg-gradient-to-r from-gray-200/20 to-gray-300/20 backdrop-blur-sm border border-gray-400/20 shadow-inner">
      <SliderPrimitive.Range className="absolute h-full bg-gradient-to-r from-blue-500 to-purple-600 shadow-sm rounded-full" />
    </SliderPrimitive.Track>
    <SliderPrimitive.Thumb className="block h-6 w-6 rounded-full border-2 border-white bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg transition-all duration-200 ease-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-400 focus-visible:ring-offset-2 hover:scale-110 hover:shadow-xl disabled:pointer-events-none disabled:opacity-50 cursor-grab active:cursor-grabbing" />
  </SliderPrimitive.Root>
))
Slider.displayName = SliderPrimitive.Root.displayName

// Enhanced Slider with value display for dual input controls
const EnhancedSlider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root> & {
    showMinMax?: boolean;
    formatValue?: (value: number) => string;
  }
>(({ className, showMinMax = true, formatValue, min = 0, max = 100, value, ...props }, ref) => {
  const displayValue = value?.[0] || min;
  const formatFn = formatValue || ((val: number) => val.toString());

  return (
    <div className="flex items-center gap-3 w-full">
      {showMinMax && (
        <span className="text-xs text-muted-foreground font-medium w-8 text-center">
          {formatFn(min)}
        </span>
      )}
      <Slider
        ref={ref}
        className={cn("flex-1", className)}
        min={min}
        max={max}
        value={value}
        {...props}
      />
      {showMinMax && (
        <span className="text-xs text-muted-foreground font-medium w-8 text-center">
          {formatFn(max)}
        </span>
      )}
    </div>
  );
});
EnhancedSlider.displayName = "EnhancedSlider";

export { Slider, EnhancedSlider }
