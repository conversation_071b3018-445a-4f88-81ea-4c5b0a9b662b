import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const loadingSpinnerVariants = cva(
  "relative inline-flex items-center justify-center",
  {
    variants: {
      size: {
        sm: "w-4 h-4",
        md: "w-8 h-8", 
        lg: "w-12 h-12",
        xl: "w-16 h-16",
      },
      variant: {
        glass: [
          "rounded-full border-2 border-glass bg-glass-spinner backdrop-blur-sm",
          "border-t-blue-500 border-r-blue-400 border-b-transparent border-l-transparent",
          "animate-glass-spin will-change-transform",
          "shadow-glass",
        ],
        gradient: [
          "rounded-full border-2 border-transparent bg-gradient-to-r from-blue-500 to-indigo-600",
          "animate-spin-smooth will-change-transform",
          "shadow-lg",
          "before:content-[''] before:absolute before:inset-1 before:rounded-full before:bg-background",
        ],
        particle: [
          "rounded-full border-2 border-blue-500/30 bg-glass-spinner backdrop-blur-sm",
          "animate-pulse-glow will-change-transform",
          "shadow-glass",
        ],
        ripple: [
          "rounded-full bg-blue-500/20 backdrop-blur-sm",
          "animate-scale-bounce will-change-transform",
        ],
      },
    },
    defaultVariants: {
      size: "md",
      variant: "glass",
    },
  }
);

const particleVariants = cva(
  "absolute rounded-full bg-blue-400/60 animate-particle-float will-change-transform",
  {
    variants: {
      size: {
        sm: "w-1 h-1",
        md: "w-1.5 h-1.5",
        lg: "w-2 h-2", 
        xl: "w-2.5 h-2.5",
      },
    },
    defaultVariants: {
      size: "md",
    },
  }
);

export interface LoadingSpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof loadingSpinnerVariants> {
  text?: string;
  showProgress?: boolean;
  progress?: number;
  showParticles?: boolean;
}

const LoadingSpinner = React.forwardRef<HTMLDivElement, LoadingSpinnerProps>(
  ({ 
    className, 
    size, 
    variant, 
    text, 
    showProgress = false, 
    progress = 0, 
    showParticles = false,
    ...props 
  }, ref) => {
    // Respect user's motion preferences
    const prefersReducedMotion = React.useMemo(() => {
      if (typeof window !== "undefined") {
        return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
      }
      return false;
    }, []);

    const spinnerClasses = React.useMemo(() => {
      if (prefersReducedMotion) {
        return cn(
          loadingSpinnerVariants({ size, variant, className }),
          "!animate-none opacity-70"
        );
      }
      return cn(loadingSpinnerVariants({ size, variant, className }));
    }, [size, variant, className, prefersReducedMotion]);

    const particles = React.useMemo(() => {
      if (!showParticles || prefersReducedMotion) return null;
      
      return Array.from({ length: 3 }, (_, i) => (
        <div
          key={i}
          className={cn(particleVariants({ size }))}
          style={{
            top: `${20 + i * 15}%`,
            left: `${20 + i * 20}%`,
            animationDelay: `${i * 0.2}s`,
          }}
        />
      ));
    }, [showParticles, size, prefersReducedMotion]);

    return (
      <div className="flex flex-col items-center justify-center space-y-3" ref={ref} {...props}>
        <div className="relative">
          <div className={spinnerClasses}>
            {/* Progress ring overlay for glass variant */}
            {showProgress && variant === "glass" && (
              <svg 
                className="absolute inset-0 w-full h-full -rotate-90"
                viewBox="0 0 32 32"
              >
                <circle
                  cx="16"
                  cy="16"
                  r="14"
                  stroke="rgba(59, 130, 246, 0.3)"
                  strokeWidth="2"
                  fill="none"
                  className="opacity-30"
                />
                <circle
                  cx="16"
                  cy="16"
                  r="14"
                  stroke="rgb(59, 130, 246)"
                  strokeWidth="2"
                  fill="none"
                  strokeDasharray={`${2 * Math.PI * 14}`}
                  strokeDashoffset={`${2 * Math.PI * 14 * (1 - progress / 100)}`}
                  className="transition-all duration-300 ease-out"
                />
              </svg>
            )}
            
            {/* Gradient variant inner content */}
            {variant === "gradient" && (
              <div className="absolute inset-1 rounded-full bg-background flex items-center justify-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
              </div>
            )}
            
            {/* Particles for particle variant */}
            {variant === "particle" && particles}
          </div>
          
          {/* Ripple effect for ripple variant */}
          {variant === "ripple" && !prefersReducedMotion && (
            <>
              <div className="absolute inset-0 rounded-full border-2 border-blue-500/40 animate-ripple" 
                   style={{ animationDelay: "0s" }} />
              <div className="absolute inset-0 rounded-full border-2 border-blue-500/30 animate-ripple" 
                   style={{ animationDelay: "0.2s" }} />
              <div className="absolute inset-0 rounded-full border-2 border-blue-500/20 animate-ripple" 
                   style={{ animationDelay: "0.4s" }} />
            </>
          )}
        </div>
        
        {/* Loading text */}
        {text && (
          <div className="text-center">
            <p className="text-sm text-muted-foreground font-medium">
              {prefersReducedMotion ? text : (
                <span className="inline-block">
                  {text}
                  <span className="animate-blink">.</span>
                  <span className="animate-blink" style={{ animationDelay: "0.2s" }}>.</span>
                  <span className="animate-blink" style={{ animationDelay: "0.4s" }}>.</span>
                </span>
              )}
            </p>
            {showProgress && (
              <p className="text-xs text-muted-foreground/70 mt-1">
                {progress}% complete
              </p>
            )}
          </div>
        )}
      </div>
    );
  }
);

LoadingSpinner.displayName = "LoadingSpinner";

export { LoadingSpinner, loadingSpinnerVariants };