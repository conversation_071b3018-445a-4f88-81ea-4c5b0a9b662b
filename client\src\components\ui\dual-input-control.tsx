import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { EnhancedSlider } from "@/components/ui/slider";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { InfoIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface DualInputControlProps {
  label: string;
  value: number;
  min: number;
  max: number;
  step?: number;
  tooltipText: string;
  inputId: string;
  onChange: (value: number) => void;
  className?: string;
  disabled?: boolean;
}

export const DualInputControl: React.FC<DualInputControlProps> = ({
  label,
  value,
  min,
  max,
  step = 1,
  tooltipText,
  inputId,
  onChange,
  className,
  disabled = false,
}) => {
  // Clamp value to valid range
  const clampedValue = Math.max(min, Math.min(max, value));

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseInt(e.target.value);
    if (!isNaN(newValue)) {
      const validValue = Math.max(min, Math.min(max, newValue));
      onChange(validValue);
    }
  };

  const handleSliderChange = (values: number[]) => {
    if (values.length > 0) {
      onChange(values[0]);
    }
  };

  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // Ensure value is clamped when input loses focus
    const newValue = parseInt(e.target.value);
    if (isNaN(newValue)) {
      onChange(min);
    } else {
      const validValue = Math.max(min, Math.min(max, newValue));
      onChange(validValue);
    }
  };

  return (
    <div className={cn("space-y-3", className)}>
      {/* Label and Tooltip */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Label 
            htmlFor={inputId} 
            className="text-sm font-medium text-foreground"
          >
            {label}
          </Label>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <InfoIcon 
                  className="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors cursor-help" 
                  aria-label={`Information about ${label}`}
                />
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-[200px]">
                <p className="text-sm">{tooltipText}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <div className="text-xs text-muted-foreground bg-accent/10 px-2 py-1 rounded-md border border-accent/20 transition-colors duration-200">
          Current: <span className="font-medium text-accent-foreground">{clampedValue}</span>
        </div>
      </div>

      {/* Input Controls */}
      <div className="grid grid-cols-1 sm:grid-cols-[1fr_2fr] gap-3 items-center">
        {/* Enhanced Number Input */}
        <div className="relative">
          <Input
            id={inputId}
            type="number"
            min={min}
            max={max}
            step={step}
            value={clampedValue}
            onChange={handleInputChange}
            onBlur={handleInputBlur}
            disabled={disabled}
            className={cn(
              "pr-12 bg-card/50 backdrop-blur-sm border-border/50 transition-all duration-200",
              "focus:bg-card/80 focus:border-primary/50 focus:shadow-lg",
              "hover:bg-card/70 hover:border-border/70",
              disabled && "opacity-50 cursor-not-allowed"
            )}
            aria-label={`${label} number input`}
            aria-describedby={`${inputId}-description`}
            role="spinbutton"
            aria-valuemin={min}
            aria-valuemax={max}
            aria-valuenow={clampedValue}
          />
          <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
            <span className="text-xs font-medium text-muted-foreground bg-accent/20 px-2 py-1 rounded transition-all duration-200">
              {clampedValue}
            </span>
          </div>
        </div>

        {/* Enhanced Slider */}
        <div className="w-full">
          <EnhancedSlider
            value={[clampedValue]}
            onValueChange={handleSliderChange}
            min={min}
            max={max}
            step={step}
            disabled={disabled}
            showMinMax={true}
            formatValue={(val) => val.toString()}
            aria-label={`${label} slider`}
            aria-valuemin={min}
            aria-valuemax={max}
            aria-valuenow={clampedValue}
            aria-valuetext={`${clampedValue} ${label.toLowerCase()}`}
            className="w-full"
          />
        </div>
      </div>

      {/* Hidden description for accessibility */}
      <div 
        id={`${inputId}-description`} 
        className="sr-only"
        aria-hidden="true"
      >
        {tooltipText}
      </div>
    </div>
  );
};

export default DualInputControl;