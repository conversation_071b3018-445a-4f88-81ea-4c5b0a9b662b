import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { BookTitle } from "@shared/types";
import { BookOpenIcon, EditIcon, TargetIcon, PaletteIcon, TypeIcon, UsersIcon } from "lucide-react";

interface AppliedTitleDisplayProps {
  title: BookTitle;
  onChangeSelection: () => void;
  showChangeSelection?: boolean;
}

export default function AppliedTitleDisplay({
  title,
  onChangeSelection,
  showChangeSelection = true
}: AppliedTitleDisplayProps) {
  return (
    <div className="mb-4">
      <div className="glass-card p-6 rounded-xl border-2 border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10 shadow-lg">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <BookOpenIcon className="h-5 w-5 text-primary" />
            <h3 className="font-semibold text-lg text-foreground">Applied Book Title</h3>
          </div>
          <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
            Applied
          </Badge>
        </div>

        {/* Main Title */}
        <div className="mb-4">
          <h2 className="font-bold text-xl text-primary leading-tight mb-2">
            {title.title}
          </h2>
          <div className="flex items-center text-sm text-muted-foreground">
            <span className="font-medium">
              {title.chapterCount} Chapters • {title.subChapterCounts.reduce((a, b) => a + b, 0)} Sub-chapters
            </span>
          </div>
        </div>

        {/* Writing Parameters Grid */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <PaletteIcon className="h-4 w-4 text-muted-foreground" />
              <div>
                <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Tone</span>
                <p className="text-sm text-foreground capitalize font-medium">{title.tone}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <TypeIcon className="h-4 w-4 text-muted-foreground" />
              <div>
                <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Language Level</span>
                <p className="text-sm text-foreground capitalize font-medium">{title.languageLevel}</p>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <EditIcon className="h-4 w-4 text-muted-foreground" />
              <div>
                <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Style</span>
                <p className="text-sm text-foreground capitalize font-medium">{title.style}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <UsersIcon className="h-4 w-4 text-muted-foreground" />
              <div>
                <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Target Audience</span>
                <p className="text-sm text-foreground font-medium">{title.targetAudience}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Book Summary */}
        {title.summary && (
          <div className="mb-4">
            <div className="flex items-center space-x-2 mb-2">
              <TargetIcon className="h-4 w-4 text-muted-foreground" />
              <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Book Summary</span>
            </div>
            <p className="text-sm text-muted-foreground leading-relaxed bg-background/50 p-3 rounded-lg border border-muted/50">
              {title.summary}
            </p>
          </div>
        )}

        {/* Chapter Structure Detail */}
        <div className="mb-4">
          <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide block mb-2">Chapter Structure</span>
          <div className="text-sm text-muted-foreground bg-background/50 p-3 rounded-lg border border-muted/50">
            {title.subChapterCounts.map((count, index) => (
              <span key={index} className="inline-block mr-3 mb-1">
                Ch.{index + 1}: {count} sub-chapters
              </span>
            ))}
          </div>
        </div>

        {/* Action Button */}
        {showChangeSelection && (
          <div className="pt-4 border-t border-primary/10">
            <Button
              onClick={onChangeSelection}
              variant="outline"
              size="sm"
              className="w-full border-primary/20 hover:border-primary/40 hover:bg-primary/5 transition-all duration-200"
            >
              <EditIcon className="h-4 w-4 mr-2" />
              Change Selection
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}