import { useLocation } from "wouter";
import { Button } from "@/components/ui/button";

export default function HomePage() {
  const [_, setLocation] = useLocation();

  const navigateToApp = () => {
    setLocation("/create-ebook");
  };

  return (
    <div className="bg-background px-4 py-12 sm:px-6 lg:px-8">
      <div className="max-w-5xl mx-auto">
        <div className="text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            AI-Powered eBook Generation
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Create professional non-fiction eBooks in minutes with our AI assistant.
          </p>
          <div className="mt-10">
            <Button size="lg" onClick={navigateToApp}>
              Get Started
            </Button>
          </div>
        </div>
        
        <div className="mt-20">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="glass-card p-6 rounded-lg shadow-glass">
              <div className="bg-primary/20 backdrop-blur-sm inline-block p-3 rounded-lg mb-4">
                <svg className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Smart Outlines</h3>
              <p className="text-muted-foreground">Generate comprehensive book outlines with detailed chapters and sub-chapters.</p>
            </div>
            
            <div className="glass-card p-6 rounded-lg shadow-glass">
              <div className="bg-primary/20 backdrop-blur-sm inline-block p-3 rounded-lg mb-4">
                <svg className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Content Generation</h3>
              <p className="text-muted-foreground">Create well-researched, contextually aware content for each section of your book.</p>
            </div>
            
            <div className="glass-card p-6 rounded-lg shadow-glass">
              <div className="bg-primary/20 backdrop-blur-sm inline-block p-3 rounded-lg mb-4">
                <svg className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Time-Saving</h3>
              <p className="text-muted-foreground">Reduce writing time from weeks to hours with our advanced AI assistant.</p>
            </div>
          </div>
        </div>
        
        <div className="mt-20 glass-card rounded-lg shadow-glass-lg overflow-hidden">
          <div className="px-6 py-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold mb-6">How It Works</h2>
              <div className="flex flex-col md:flex-row justify-center items-center space-y-8 md:space-y-0 md:space-x-12">
                <div className="flex flex-col items-center max-w-xs">
                  <div className="bg-primary/80 backdrop-blur-sm w-12 h-12 rounded-full flex items-center justify-center mb-4 shadow-glass">
                    <span className="text-primary-foreground font-bold">1</span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Enter Your Topic</h3>
                  <p className="text-muted-foreground text-center">Provide your book topic or keywords</p>
                </div>
                
                <div className="flex flex-col items-center max-w-xs">
                  <div className="bg-primary/80 backdrop-blur-sm w-12 h-12 rounded-full flex items-center justify-center mb-4 shadow-glass">
                    <span className="text-primary-foreground font-bold">2</span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Review Outline</h3>
                  <p className="text-muted-foreground text-center">Get a detailed chapter structure for your book</p>
                </div>
                
                <div className="flex flex-col items-center max-w-xs">
                  <div className="bg-primary/80 backdrop-blur-sm w-12 h-12 rounded-full flex items-center justify-center mb-4 shadow-glass">
                    <span className="text-primary-foreground font-bold">3</span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Generate Content</h3>
                  <p className="text-muted-foreground text-center">Select chapters to create detailed content</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-20 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Create Your eBook?</h2>
          <Button size="lg" onClick={navigateToApp}>
            Start Writing Now
          </Button>
        </div>
      </div>
    </div>
  );
}
