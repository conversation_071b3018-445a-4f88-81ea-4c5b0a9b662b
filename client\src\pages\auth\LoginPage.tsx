import { useState } from "react";
import { Link, useLocation } from "wouter";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export default function LoginPage() {
  const { login, loginWithGoogle, error, redirectToApp } = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [_, setLocation] = useLocation();

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      await login(email, password);
      // Clear form state on successful login
      setEmail("");
      setPassword("");
      // Redirect will be handled automatically by AuthContext
      redirectToApp();
    } catch (error) {
      // Error is handled by AuthContext, just keep loading state
    } finally {
      setLoading(false);
    }
  };

  const onGoogle = async () => {
    setLoading(true);
    try {
      await loginWithGoogle();
      // Redirect will be handled automatically by AuthContext
      redirectToApp();
    } catch (error) {
      // Error is handled by AuthContext, just keep loading state
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center py-16 px-4">
      <div className="glass-card w-full max-w-md rounded-xl p-8">
        <h2 className="text-2xl font-semibold mb-1">Welcome back</h2>
        <p className="text-sm text-muted-foreground mb-6">Sign in to continue</p>
        {error && (
          <div className="mb-4 text-sm text-red-400">{error}</div>
        )}
        <form onSubmit={onSubmit} className="space-y-4">
          <div>
            <label className="block text-sm mb-1">Email</label>
            <Input
              className="glass-input"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              placeholder="<EMAIL>"
            />
          </div>
          <div>
            <label className="block text-sm mb-1">Password</label>
            <Input
              className="glass-input"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              placeholder="••••••••"
            />
          </div>
          <Button className="w-full gradient-button" type="submit" disabled={loading}>
            {loading ? "Signing in…" : "Sign In"}
          </Button>
        </form>
        <div className="mt-4">
          <Button variant="outline" className="w-full flex items-center justify-center gap-2" onClick={onGoogle} disabled={loading}>
            <span className="bg-white rounded-sm p-0.5"><img src="https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg" alt="Google" className="w-4 h-4" /></span>
            <span className="font-medium">Sign in with Google</span>
          </Button>
        </div>
        <div className="mt-4 text-sm flex justify-between">
          <Link href="/forgot-password"><a className="text-primary hover:underline">Forgot password?</a></Link>
          <Link href="/signup"><a className="text-primary hover:underline">Create account</a></Link>
        </div>
      </div>
    </div>
  );
}

