Install Netlify CLI (if not already installed):

npm install -g netlify-cli

Login to Net<PERSON> (if not already logged in):

netlify login

Quick Deploy (Recommended)
# Navigate to your project directory
cd "c:\Users\<USER>\OneDrive\Desktop\Cursor Full-Stack Apps\NonFictionBookBuilder"
# Build the project
npm run build
# Deploy to production
netlify deploy --prod

(npm run build
netlify deploy --prod)

Useful Commands
Check deployment status:
netlify status
View site info:
netlify sites:list
Open deployed site in browser:
netlify open
View build logs:
netlify logs
Link to different site (if needed):
netlify link

Command Line Environments
You can run these commands in any of the following:

Command Prompt (CMD)
PowerShell
Windows Terminal
Git Bash
VS Code Terminal

Important Notes
Always build first: Run npm run build before deploying to ensure you're deploying the latest changes
Check status: Use netlify status to verify you're connected to the correct site
Environment variables: If you have environment variables, make sure they're set in the Netlify dashboard
Custom domain: Your site is already configured with the custom domain aiebookwriterpro.netlify.app

Troubleshooting
If you encounter issues:

Authentication problems: Run netlify logout then netlify login
Build failures: Check the build logs in the Netlify dashboard
Site not linked: Run netlify link and select your site
Function errors: Check function logs at https://app.netlify.com/projects/aiebookwriterpro/logs/functions
