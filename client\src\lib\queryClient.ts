import { QueryClient, QueryFunction } from "@tanstack/react-query";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    let errorMessage = res.statusText;

    try {
      const responseText = await res.text();
      if (responseText) {
        // Try to parse JSON error response
        try {
          const errorData = JSON.parse(responseText);
          errorMessage = errorData.message || errorMessage;
        } catch {
          // If not JSON, use the raw text
          errorMessage = responseText;
        }
      }
    } catch {
      // If we can't read the response, use status text
      errorMessage = res.statusText;
    }

    // Create a more user-friendly error message based on status code
    let userFriendlyMessage = errorMessage;

    if (res.status === 429) {
      userFriendlyMessage = "API rate limit exceeded. Please wait a moment and try again.";
    } else if (res.status === 401) {
      userFriendlyMessage = "Authentication error. Please check your configuration.";
    } else if (res.status === 503) {
      userFriendlyMessage = "Service temporarily unavailable. Please try again later.";
    } else if (res.status >= 500) {
      userFriendlyMessage = "Server error. Please try again in a moment.";
    }

    throw new Error(`${res.status}: ${userFriendlyMessage}`);
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  const res = await fetch(url, {
    method,
    headers: data ? { "Content-Type": "application/json" } : {},
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include",
  });

  await throwIfResNotOk(res);
  return res;
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    const res = await fetch(queryKey[0] as string, {
      credentials: "include",
    });

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      return null;
    }

    await throwIfResNotOk(res);
    return await res.json();
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});
