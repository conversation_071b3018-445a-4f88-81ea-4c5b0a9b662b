import { useState, useEffect, useCallback, useRef } from "react";
import OutlineGenerator from "@/components/book/OutlineGenerator";
import ContentGenerator from "@/components/book/ContentGenerator";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useAuth } from "@/context/AuthContext";
import { useAppData } from "@/context/AppDataContext";
import { ProjectService } from "@/lib/projectService";
import { SavedIdeasService } from "@/lib/savedIdeasService";
import { refreshAllDataCounts } from "@/hooks/use-data-counts";
import { getNextChapterToGenerate } from "@/lib/projectUtils";
import {
  BookOutline,
  Chapter,
  GeneratedContent,
  OutlineGenerationParams,
  WritingTone,
  WritingStyle,
  WritingLanguage,
  BookTitle,
  BookLanguage,
  TargetAudience,
  BookSize
} from "@shared/types";
import { adjustChapterStructureToSize } from "@shared/bookSizeUtils";

// Utility function to validate and sanitize outline parameters
const validateOutlineParams = (params: OutlineGenerationParams): OutlineGenerationParams => {
  return {
    ...params,
    maxChapters: Math.max(5, params.maxChapters), // Ensure minimum 5 chapters
    maxSubChapters: Math.max(3, params.maxSubChapters), // Ensure minimum 3 sub-chapters
  };
};

export default function AppPage() {
  // Initialize toast and auth
  const { toast } = useToast();
  const { user } = useAuth();
  const {
    savedIdeaData,
    setSavedIdeaData,
    projectToLoad,
    setProjectToLoad,
    autoLoadChapterData,
    setAutoLoadChapterData,
    currentProjectId,
    setCurrentProjectId,
    chapterContent,
    getChapterContent,
    setChapterContent,
    setDeletedIdeaId
  } = useAppData();

  // Book content state
  const [bookTopic, setBookTopic] = useState<string>("");
  const [outline, setOutline] = useState<BookOutline | null>(null);
  const [selectedChapter, setSelectedChapter] = useState<{
    mainChapterTitle: string;
    subChapterTitle: string;
    index: number;
    chapterIndex: number;
    subChapterIndex: number;
  } | null>(null);
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);
  const [generatedChapters, setGeneratedChapters] = useState<Record<string, boolean>>({});

  // Generation parameters state
  const [outlineParams, setOutlineParams] = useState<OutlineGenerationParams>({
    maxChapters: 5,
    maxSubChapters: 3,
    targetAudience: "General Audience"
  });

  const [contentTone, setContentTone] = useState<WritingTone>("professional");
  const [contentStyle, setContentStyle] = useState<WritingStyle>("descriptive");
  const [contentLanguage, setContentLanguage] = useState<WritingLanguage>("intermediate");
  const [selectedLanguage, setSelectedLanguage] = useState<BookLanguage>("en");
  const [targetAudience, setTargetAudience] = useState<TargetAudience>("General Audience");
  const [bookSummary, setBookSummary] = useState<string>("");

  // New state for two-step workflow
  const [workflowStep, setWorkflowStep] = useState<'input' | 'title-selection' | 'outline-generated'>('input');
  const [generatedTitles, setGeneratedTitles] = useState<BookTitle[]>([]);
  const [selectedTitle, setSelectedTitle] = useState<BookTitle | null>(null);
  const [isGeneratingOutline, setIsGeneratingOutline] = useState(false);

  // Book size state
  const [selectedBookSize, setSelectedBookSize] = useState<BookSize>('small');
  const [appliedTitleName, setAppliedTitleName] = useState<string | undefined>(undefined);
  
  // Project context state to track if user is continuing existing project or creating new
  const [projectContext, setProjectContext] = useState<'new' | 'existing' | null>(null);
  
  // Save Idea state
  const [isSavingIdea, setIsSavingIdea] = useState(false);

  // Project loading on component mount
  useEffect(() => {
    loadProjectIfExists();
  }, []);

  // Function to load project from context (set by CurrentProjectPage)
  const loadProjectIfExists = async () => {
    if (projectToLoad) {
      try {
        await loadProject(projectToLoad);

        // Clear the load flag
        setProjectToLoad(null);
      } catch (error) {
        console.error('Error loading project:', error);
        toast({
          title: "Load failed",
          description: "Could not load the selected project.",
          variant: "destructive"
        });
      }
    }

    // Check for saved idea workflow
    if (savedIdeaData) {
      try {
        loadFromSavedIdea(savedIdeaData);

        // Don't clear the saved idea flag here - it will be cleared when outline is generated
        // This preserves the idea ID for proper deletion timing
      } catch (error) {
        console.error('Error loading saved idea:', error);
        toast({
          title: "Load failed",
          description: "Could not load the saved idea.",
          variant: "destructive"
        });
        // Clear invalid data on error
        setSavedIdeaData(null);
      }
    }
  };

  // Function to load chapter content from subcollections
  const loadChapterContent = async (projectId: string) => {
    try {
      const chapterContent = await ProjectService.getAllChapterContents(projectId);
      Object.entries(chapterContent).forEach(([key, content]) => {
        setChapterContent(key, content);
      });
      console.log('Loaded chapter content from subcollections:', Object.keys(chapterContent));
    } catch (error) {
      console.error('Error loading chapter content:', error);
      // Don't show error to user as this is not critical for project loading
    }
  };

  // Function to load a saved project
  const loadProject = async (project: any) => {
    try {
      // Restore all project data
      setBookTopic(project.topic || '');
      setOutline(project.outline || null);
      setOutlineParams(project.generationParams || {
        maxChapters: 5,
        maxSubChapters: 3,
        targetAudience: "General Audience"
      });

      if (project.selectedTitle) {
        setSelectedTitle(project.selectedTitle);
        setGeneratedTitles([project.selectedTitle]);
        setAppliedTitleName(project.selectedTitle.title);
      }

      if (project.writingParams) {
        setContentTone(project.writingParams.tone || 'professional');
        setContentStyle(project.writingParams.style || 'descriptive');
        setContentLanguage(project.writingParams.language || 'intermediate');
        setSelectedLanguage(project.writingParams.targetLanguage || 'en');
        setTargetAudience(project.writingParams.targetAudience || 'General Audience');
      }

      setBookSummary(project.bookSummary || '');
      setGeneratedChapters(project.generatedChapters || {});

      // Set the next chapter to generate based on current progress
      if (project.outline && project.generatedChapters) {
        const nextChapter = getNextChapterToGenerate(project.outline, project.generatedChapters);
        if (nextChapter) {
          setNextChapterToGenerate(nextChapter);
        }
      }

      // Load chapter content from subcollections (new approach)
      if (project.id) {
        await loadChapterContent(project.id);
      }

      // Fallback: Load chapter content from main document if it exists (for backward compatibility)
      if (project.chapterContent) {
        Object.entries(project.chapterContent).forEach(([key, content]) => {
          setChapterContent(key, content as string);
        });
      }

      // Set workflow step based on what data exists
      if (project.outline) {
        setWorkflowStep('outline-generated');
      } else if (project.selectedTitle) {
        setWorkflowStep('title-selection');
      } else {
        setWorkflowStep('input');
      }

      // Set project context to 'existing' since this is loaded from Continue button
      setProjectContext('existing');

      // Store the current project ID for updates
      setCurrentProjectId(project.id);

      // Handle auto-loading of last generated sub-chapter if requested
      if (autoLoadChapterData?.shouldAutoLoad && autoLoadChapterData.lastGeneratedChapter) {
        const lastChapter = autoLoadChapterData.lastGeneratedChapter;

        // Auto-select the last generated chapter
        setTimeout(() => {
          handleChapterSelect(
            lastChapter.mainChapterTitle,
            lastChapter.subChapterTitle,
            lastChapter.index,
            lastChapter.chapterIndex,
            lastChapter.subChapterIndex
          );
        }, 100); // Small delay to ensure all state is set

        // Clear the auto-load data after using it
        setAutoLoadChapterData(null);

        toast({
          title: "Project loaded",
          description: `Successfully loaded "${project.title}" - Showing last generated chapter: ${lastChapter.subChapterTitle}`,
        });
      } else {
        toast({
          title: "Project loaded",
          description: `Successfully loaded "${project.title}"`,
        });
      }
    } catch (error) {
      console.error('Error in loadProject:', error);
      toast({
        title: "Load failed",
        description: "Could not load the project data.",
        variant: "destructive"
      });
    }
  };
  
  // Function to load from saved idea
  const loadFromSavedIdea = (ideaData: any) => {
    try {
      console.log('Loading saved idea:', ideaData);
      
      // Set the book topic
      setBookTopic(ideaData.originalTopic);
      
      // Set the selected title
      setSelectedTitle(ideaData.selectedTitle);
      setGeneratedTitles([ideaData.selectedTitle]);
      setAppliedTitleName(ideaData.selectedTitle.title);
      
      console.log('Selected title set to:', ideaData.selectedTitle);
      
      // Apply title parameters
      if (ideaData.selectedTitle.tone) {
        setContentTone(ideaData.selectedTitle.tone);
      }
      if (ideaData.selectedTitle.style) {
        setContentStyle(ideaData.selectedTitle.style);
      }
      if (ideaData.selectedTitle.languageLevel) {
        setContentLanguage(ideaData.selectedTitle.languageLevel);
      }
      if (ideaData.selectedTitle.targetAudience) {
        setTargetAudience(ideaData.selectedTitle.targetAudience);
      }
      if (ideaData.selectedTitle.summary) {
        setBookSummary(ideaData.selectedTitle.summary);
      }
      if (ideaData.selectedTitle.targetLanguage) {
        setSelectedLanguage(ideaData.selectedTitle.targetLanguage);
      }
      
      // Set outline parameters based on the saved title
      // Use the original chapter structure from saved title without size constraints
      if (ideaData.selectedTitle.chapterCount && ideaData.selectedTitle.subChapterCounts) {
        const originalChapterCount = ideaData.selectedTitle.chapterCount;
        const originalSubChapterCounts = ideaData.selectedTitle.subChapterCounts;

        const avgSubChapters = Math.max(3, Math.ceil(
          originalSubChapterCounts.reduce((sum: number, count: number) => sum + count, 0) / originalChapterCount
        ));

        setOutlineParams(prev => ({
          ...prev,
          maxChapters: originalChapterCount,
          maxSubChapters: avgSubChapters,
          subChapterCounts: originalSubChapterCounts,
          bookSize: selectedBookSize
        }));
      }
      
      // Set workflow to title-selection so user can proceed to generate outline
      setWorkflowStep('title-selection');
      // Set project context to 'existing' for saved ideas to hide Change Selection button
      setProjectContext('existing');
      
      console.log('Workflow step set to title-selection, selectedTitle:', ideaData.selectedTitle?.title);
      
      toast({
        title: "Ready to start writing",
        description: `Loaded idea: "${ideaData.selectedTitle.title}". Click 'Generate Outline' to continue.`,
      });
      
      // Auto-generate outline after a short delay to let the UI update
      // Removed to let user manually click "Generate Outline" for consistent behavior
      // setTimeout(() => {
      //   handleGenerateOutline();
      // }, 1000);
    } catch (error) {
      console.error('Error in loadFromSavedIdea:', error);
      toast({
        title: "Load failed",
        description: "Could not load the saved idea.",
        variant: "destructive"
      });
    }
  };

  const handleOutlineGenerated = async (newOutline: BookOutline) => {
    setOutline(newOutline);
    setSelectedChapter(null);
    setGeneratedContent(null);
    setWorkflowStep('outline-generated');

    // Check if this is a saved idea workflow and delete the idea after successful outline generation
    if (savedIdeaData) {
      try {
        if (savedIdeaData.savedIdeaId) {
          console.log('Deleting saved idea after successful outline generation:', savedIdeaData.savedIdeaId);
          await SavedIdeasService.deleteIdea(savedIdeaData.savedIdeaId);
          console.log('Saved idea deleted successfully');

          // Notify other components about the deletion
          setDeletedIdeaId(savedIdeaData.savedIdeaId);

          // Clear the context data since we've processed it
          setSavedIdeaData(null);

          toast({
            title: "Saved idea converted",
            description: "Your saved idea has been converted to a project.",
          });
        }
      } catch (error) {
        console.error('Error deleting saved idea:', error);
        // Don't fail the outline generation if deletion fails
        toast({
          title: "Warning",
          description: "Outline generated successfully, but failed to remove saved idea.",
          variant: "destructive"
        });
      }
    }

    // Auto-save project after outline generation
    await saveProject(newOutline);
  };

  // Function to save project to Firestore
  const saveProject = async (outlineToSave: BookOutline) => {
    if (!user?.uid) {
      console.warn('No user logged in, cannot save project');
      toast({
        title: "Authentication required",
        description: "Please log in to save your project.",
        variant: "destructive"
      });
      return;
    }

    if (!user.emailVerified) {
      toast({
        title: "Email verification required",
        description: "Please verify your email address to save projects.",
        variant: "destructive"
      });
      return;
    }

    try {
      console.log('Attempting to save project for user:', user.uid);
      
      const projectTitle = selectedTitle?.title || 
        (bookTopic.length > 50 ? `${bookTopic.substring(0, 47)}...` : bookTopic) || 
        'Untitled Book Project';
      
      const projectData = {
        title: projectTitle,
        topic: bookTopic,
        outline: outlineToSave,
        generationParams: {
          ...outlineParams,
          targetLanguage: selectedLanguage,
          summary: bookSummary
        },
        selectedTitle: selectedTitle || undefined,
        writingParams: {
          tone: contentTone,
          style: contentStyle,
          language: contentLanguage,
          targetLanguage: selectedLanguage,
          targetAudience: targetAudience,
        },
        status: 'outline_generated' as const,
        generatedChapters: generatedChapters,
        bookSummary: bookSummary
      };

      console.log('Project data prepared:', { ...projectData, outline: '[OUTLINE_DATA]' });
      
      const projectId = await ProjectService.saveProject(user.uid, projectData);
      
      // Refresh navbar count badges
      refreshAllDataCounts();
      
      // Store the project ID for future updates
      setCurrentProjectId(projectId);
      
      console.log('Project saved successfully with ID:', projectId);
      
      toast({
        title: "Project saved",
        description: "Your book project has been saved successfully",
      });
    } catch (error) {
      console.error('Error saving project:', error);
      
      let errorMessage = "Could not save your project. Your work is still available in this session.";
      
      if (error instanceof Error) {
        if (error.message.includes('Missing or insufficient permissions')) {
          errorMessage = "Permission denied. Please check your authentication and try again.";
        } else if (error.message.includes('User must be authenticated')) {
          errorMessage = "Authentication required. Please log in and try again.";
        }
      }
      
      toast({
        title: "Save failed",
        description: errorMessage,
        variant: "destructive"
      });
    }
  };

  // Handler for when titles are generated
  const handleTitlesGenerated = (titles: BookTitle[]) => {
    setGeneratedTitles(titles);
  };

  // Handler for title selection
  const handleTitleSelected = (title: BookTitle) => {
    setSelectedTitle(title);
    // Set project context to 'new' since user is actively selecting a title
    setProjectContext('new');
  };

  // Handler for applying parameters from selected title
  const handleApplyParameters = (title: BookTitle) => {
    // Use the original chapter structure from the title without size constraints
    // This preserves the intended structure of the saved title
    if (title.chapterCount && title.subChapterCounts) {
      const originalChapterCount = title.chapterCount;
      const originalSubChapterCounts = title.subChapterCounts;

      // Calculate average sub-chapters per chapter (rounded up to ensure we don't lose content)
      const avgSubChapters = Math.max(3, Math.ceil(
        originalSubChapterCounts.reduce((sum: number, count: number) => sum + count, 0) / originalChapterCount
      ));

      setOutlineParams(prev => ({
        ...prev,
        maxChapters: originalChapterCount,
        maxSubChapters: avgSubChapters, // Keep for backward compatibility
        subChapterCounts: originalSubChapterCounts, // Preserve original dynamic distribution
        bookSize: selectedBookSize
      }));
    }

    // Auto-populate writing style parameters from selected title
    if (title.tone) {
      setContentTone(title.tone);
    }
    if (title.style) {
      setContentStyle(title.style);
    }
    if (title.languageLevel) {
      setContentLanguage(title.languageLevel);
    }
    if (title.targetAudience) {
      setTargetAudience(title.targetAudience);
    }
    if (title.summary) {
      setBookSummary(title.summary);
    }
    if (title.targetLanguage) {
      setSelectedLanguage(title.targetLanguage);
    }

    // Store applied title name for display
    setAppliedTitleName(title.title);

    toast({
      title: "Parameters applied successfully",
      description: `Form fields updated with "${title.title}" parameters, adjusted for ${selectedBookSize} book size.`,
    });
  };

  // Handler for workflow step changes
  const handleWorkflowStepChange = (step: 'input' | 'title-selection' | 'outline-generated') => {
    setWorkflowStep(step);
    if (step === 'input') {
      // Reset all state when starting over
      setGeneratedTitles([]);
      setSelectedTitle(null);
      setOutline(null);
      setSelectedChapter(null);
      setGeneratedContent(null);
      setAppliedTitleName(undefined);
      setBookSummary(""); // Reset book summary
      setGeneratedChapters({});
      // Reset project context when starting fresh
      setProjectContext(null);
      
      // Clear current project ID since we're starting fresh
      localStorage.removeItem('currentProjectId');
      
      // Clear saved idea data since user is starting over
      localStorage.removeItem('startFromSavedIdea');
    }
  };

  // Handler for book size changes
  const handleBookSizeChange = (size: BookSize) => {
    setSelectedBookSize(size);

    // Update outline params with new book size
    setOutlineParams(prev => ({
      ...prev,
      bookSize: size
    }));

    // Clear applied title name since size changed
    setAppliedTitleName(undefined);
  };

  // Handler for changing title selection (going back to title selection step)
  const handleChangeSelection = () => {
    // Clear the applied title but keep the generated titles and selected title
    setAppliedTitleName(undefined);
    // Go back to title selection step
    setWorkflowStep('title-selection');
    
    toast({
      title: "Selection cleared",
      description: "You can now select a different book title",
    });
  };

  // Handler for outline generation from ContentGenerator
  const handleGenerateOutline = async () => {
    if (!selectedTitle) {
      toast({
        title: "Title selection required",
        description: "Please select a book title before generating the outline",
        variant: "destructive"
      });
      return;
    }
    
    setIsGeneratingOutline(true);
    
    try {
      
      const res = await apiRequest('POST', '/api/generate-outline', { 
        selectedTitle: selectedTitle,
        userInput: bookTopic,
        generationParams: validateOutlineParams({
          ...outlineParams,
          summary: bookSummary
        })
      });
      const data = await res.json();
      handleOutlineGenerated(data.outline);
      
      toast({
        title: "Outline generated",
        description: "Your book outline has been created successfully",
      });
    } catch (err) {
      console.error("Error generating outline:", err);
      toast({
        title: "Generation failed",
        description: "Could not generate outline. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingOutline(false);
    }
  };

  // Track the next chapter to be generated (sequential order)
  const [nextChapterToGenerate, setNextChapterToGenerate] = useState<{
    chapterIndex: number;
    subChapterIndex: number;
  }>({ chapterIndex: 0, subChapterIndex: 0 });

  // Track if batch generation is currently in progress
  const [isBatchGenerationActive, setIsBatchGenerationActive] = useState(false);

  const handleChapterSelect = (mainChapterTitle: string, subChapterTitle: string, index: number, chapterIndex: number, subChapterIndex: number) => {
    // Check if this chapter has already been generated
    const isGenerated = generatedChapters[`${mainChapterTitle}-${subChapterTitle}`];

    // If it's already generated, just show the content without regenerating
    if (isGenerated) {
      // Find the existing content
      const existingContent = outline?.chapters.flatMap((chapter, cIdx) =>
        chapter.subchapters.map((subchapter, sIdx) => ({
          mainChapterTitle: chapter.title,
          subChapterTitle: subchapter,
          index: cIdx + 1,
          chapterIndex: cIdx,
          subChapterIndex: sIdx,
          content: generatedChapters[`${chapter.title}-${subchapter}`] ?
            getChapterContent(`${chapter.title}-${subchapter}`) || '' : ''
        }))
      ).find(c => c.mainChapterTitle === mainChapterTitle && c.subChapterTitle === subChapterTitle);

      if (existingContent && existingContent.content) {
        setSelectedChapter({
          mainChapterTitle,
          subChapterTitle,
          index,
          chapterIndex,
          subChapterIndex
        });

        setGeneratedContent({
          mainChapterTitle,
          subChapterTitle,
          content: existingContent.content,
          index
        });
        return;
      }
    }

    // Check if this is the next chapter in sequence
    const isNextInSequence =
      (chapterIndex === nextChapterToGenerate.chapterIndex &&
       subChapterIndex === nextChapterToGenerate.subChapterIndex);

    // NEW LOGIC: Check if this is within the same chapter during batch generation
    // This allows sequential generation within the same chapter during "Generate All Sub-Chapters"
    const isWithinSameChapterBatch = isBatchGenerationActive && 
      chapterIndex === nextChapterToGenerate.chapterIndex &&
      subChapterIndex >= nextChapterToGenerate.subChapterIndex;

    // Check if all previous chapters are completed (for cross-chapter sequential requirement)
    const allPreviousChaptersCompleted = () => {
      if (!outline || chapterIndex <= 0) return true;
      
      for (let i = 0; i < chapterIndex; i++) {
        const chapter = outline.chapters[i];
        const allSubChaptersGenerated = chapter.subchapters.every(subchapter => 
          generatedChapters[`${chapter.title}-${subchapter}`]
        );
        if (!allSubChaptersGenerated) return false;
      }
      return true;
    };

    // Allow generation if:
    // 1. It's the next in sequence, OR
    // 2. It's within the same chapter during batch generation AND all previous chapters are completed
    if (isNextInSequence || (isWithinSameChapterBatch && allPreviousChaptersCompleted())) {
      console.log('📝 Chapter selection allowed:', {
        mainChapterTitle,
        subChapterTitle,
        chapterIndex,
        subChapterIndex,
        isNextInSequence,
        isWithinSameChapterBatch,
        isBatchGenerationActive,
        nextChapterToGenerate
      });
      
      // Reset generated content when selecting a new chapter
      setGeneratedContent(null);
      setSelectedChapter({
        mainChapterTitle,
        subChapterTitle,
        index,
        chapterIndex,
        subChapterIndex
      });
    } else if (!isGenerated) {
      console.log('❌ Chapter selection blocked:', {
        mainChapterTitle,
        subChapterTitle,
        chapterIndex,
        subChapterIndex,
        isNextInSequence,
        isWithinSameChapterBatch,
        isBatchGenerationActive,
        nextChapterToGenerate,
        allPreviousCompleted: allPreviousChaptersCompleted()
      });
      
      // If trying to select a chapter out of sequence that hasn't been generated yet
      const errorMessage = isBatchGenerationActive 
        ? "Batch generation is in progress. Please wait for the current chapter to complete."
        : "Please generate content for chapters in sequential order.";
        
      toast({
        title: "Sequential generation required",
        description: errorMessage,
        variant: "destructive"
      });
    }
  };

  const handleContentGenerated = async (content: string) => {
    if (selectedChapter) {
      const newContent: GeneratedContent = {
        mainChapterTitle: selectedChapter.mainChapterTitle,
        subChapterTitle: selectedChapter.subChapterTitle,
        content,
        index: selectedChapter.index
      };

      setGeneratedContent(newContent);

      const chapterKey = `${selectedChapter.mainChapterTitle}-${selectedChapter.subChapterTitle}`;

      // Store the content in context for immediate retrieval
      setChapterContent(chapterKey, content);

      // Mark this chapter as generated
      const updatedGeneratedChapters = {
        ...generatedChapters,
        [chapterKey]: true
      };

      setGeneratedChapters(updatedGeneratedChapters);

      // Save chapter content to Firestore subcollection if we have a current project ID
      if (currentProjectId) {
        try {
          await ProjectService.updateChapterContent(
            currentProjectId,
            chapterKey,
            content,
            selectedChapter.mainChapterTitle,
            selectedChapter.subChapterTitle
          );
          console.log('Chapter content saved to Firestore subcollection:', chapterKey);
        } catch (error) {
          console.error('Error saving chapter content to Firestore:', error);
          toast({
            title: "Warning",
            description: "Chapter generated but failed to save to database.",
            variant: "destructive"
          });
        }
      }

      // Update project in Firestore with new generated chapters
      await updateProjectProgress(updatedGeneratedChapters);

      // Update the next chapter to generate
      if (outline) {
        let nextChapterIndex = selectedChapter.chapterIndex;
        let nextSubChapterIndex = selectedChapter.subChapterIndex + 1;

        // If we've reached the end of subchapters in this chapter, move to the next chapter
        if (nextSubChapterIndex >= outline.chapters[nextChapterIndex].subchapters.length) {
          nextChapterIndex++;
          nextSubChapterIndex = 0;
        }

        // Only update if we haven't reached the end of all chapters
        if (nextChapterIndex < outline.chapters.length) {
          setNextChapterToGenerate({
            chapterIndex: nextChapterIndex,
            subChapterIndex: nextSubChapterIndex
          });
        }
      }
    }
  };

  // Function to update project progress in Firestore
  const updateProjectProgress = async (updatedGeneratedChapters: Record<string, boolean>) => {
    const projectId = currentProjectId;
    if (!projectId || !user?.uid) return;

    try {
      await ProjectService.updateGeneratedChapters(projectId, updatedGeneratedChapters);
      
      // Update status if all chapters are completed
      const totalChapters = getTotalSubchaptersCount();
      const completedChapters = Object.values(updatedGeneratedChapters).filter(Boolean).length;
      
      if (completedChapters === totalChapters && totalChapters > 0) {
        await ProjectService.updateProjectStatus(projectId, 'completed');
        toast({
          title: "Book completed!",
          description: "Congratulations! You have completed your entire book.",
        });
      } else if (completedChapters > 0) {
        await ProjectService.updateProjectStatus(projectId, 'content_in_progress');
      }
    } catch (error) {
      console.error('Error updating project progress:', error);
      // Don't show error to user since the content was still generated successfully
    }
  };

  // Function to handle saving a book idea
  const handleSaveIdea = async (title: BookTitle) => {
    if (!user?.uid) {
      toast({
        title: "Authentication required",
        description: "Please log in to save ideas.",
        variant: "destructive"
      });
      return;
    }

    if (!bookTopic.trim()) {
      toast({
        title: "Missing topic",
        description: "Please enter a book topic before saving the idea.",
        variant: "destructive"
      });
      return;
    }

    console.log('Starting save idea process for:', title.title, 'User:', user.uid);

    try {
      setIsSavingIdea(true);
      
      // Check if this idea is already saved
      console.log('Checking if idea already exists...');
      let isAlreadySaved = false;
      
      try {
        isAlreadySaved = await SavedIdeasService.isIdeaAlreadySaved(user.uid, title, bookTopic.trim());
        console.log('Already saved check result:', isAlreadySaved);
      } catch (duplicateCheckError) {
        console.warn('Error during duplicate check, proceeding with save:', duplicateCheckError);
        // Continue with save operation even if duplicate check fails
      }
      
      if (isAlreadySaved) {
        console.log('Idea already exists, showing warning');
        toast({
          title: "Already saved",
          description: "This idea has already been saved.",
          variant: "destructive"
        });
        return;
      }

      console.log('Proceeding to save idea...');
      // Enhanced idea data to include current target language and other state
      const enhancedTitle = {
        ...title,
        targetLanguage: selectedLanguage, // Include current target language
      };
      
      const ideaData = {
        bookTitle: enhancedTitle,
        originalTopic: bookTopic.trim()
      };

      const savedId = await SavedIdeasService.saveIdea(user.uid, ideaData);
      console.log('Idea saved successfully with ID:', savedId);
      
      // Refresh navbar count badges
      refreshAllDataCounts();
      
      toast({
        title: "Idea saved",
        description: `"${title.title}" has been saved to your ideas.`,
      });
    } catch (error) {
      console.error('Error saving idea:', error);
      
      // Provide more specific error messages based on error type
      let errorMessage = "Could not save the idea. Please try again.";
      
      if (error instanceof Error) {
        if (error.message.includes('authentication')) {
          errorMessage = "Authentication error. Please log in again.";
        } else if (error.message.includes('permission')) {
          errorMessage = "Permission denied. Please check your account settings.";
        } else if (error.message.includes('network')) {
          errorMessage = "Network error. Please check your connection and try again.";
        }
      }
      
      toast({
        title: "Save failed",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsSavingIdea(false);
    }
  };

  const handleOutlineParamsChange = (params: OutlineGenerationParams) => {
    // Validate parameters before setting
    const validatedParams = validateOutlineParams(params);
    setOutlineParams(validatedParams);
  };

  const handleTargetAudienceChange = (audience: TargetAudience) => {
    setTargetAudience(audience);
    setOutlineParams(prev => ({
      ...prev,
      targetAudience: audience
    }));
  };

  const getGeneratedChaptersCount = () => {
    return Object.keys(generatedChapters).length;
  };

  const getTotalSubchaptersCount = () => {
    if (!outline) return 0;
    return outline.chapters.reduce((count, chapter) => {
      return count + chapter.subchapters.length;
    }, 0);
  };

      {/* Verification banner for unverified users appears globally via App if needed */
      }

  return (
    <div className="flex-1 flex flex-col md:flex-row min-h-screen bg-gradient-to-br from-background via-background/95 to-background/90">
      {/* Left Panel: Input and Outline - Premium Glass Design */}
      <div className="w-full md:w-2/5 lg:w-1/3 glass-card border-r-2 border-glass-subtle backdrop-blur-lg shadow-glass-lg transition-all duration-300">
        <OutlineGenerator
          onOutlineGenerated={handleOutlineGenerated}
          onBookTopicChange={setBookTopic}
          bookTopic={bookTopic}
          outline={outline}
          onChapterSelect={handleChapterSelect}
          generatedChapters={generatedChapters}
          generatedCount={getGeneratedChaptersCount()}
          totalCount={getTotalSubchaptersCount()}
          outlineParams={outlineParams}
          onOutlineParamsChange={handleOutlineParamsChange}
          contentTone={contentTone}
          onContentToneChange={setContentTone}
          contentStyle={contentStyle}
          onContentStyleChange={setContentStyle}
          contentLanguage={contentLanguage}
          onContentLanguageChange={setContentLanguage}
          selectedLanguage={selectedLanguage}
          onLanguageChange={setSelectedLanguage}
          targetAudience={targetAudience}
          onTargetAudienceChange={handleTargetAudienceChange}
          bookSummary={bookSummary}
          onBookSummaryChange={setBookSummary}
          nextChapterToGenerate={nextChapterToGenerate}
          onTitleSelected={handleTitleSelected}
          onTitlesGenerated={handleTitlesGenerated}
          generatedTitles={generatedTitles}
          selectedTitle={selectedTitle}
          workflowStep={workflowStep}
          onWorkflowStepChange={handleWorkflowStepChange}
          selectedBookSize={selectedBookSize}
          onBookSizeChange={handleBookSizeChange}
          appliedTitleName={appliedTitleName}
          onChangeSelection={handleChangeSelection}
          showChangeSelection={projectContext === 'new'}
          onBatchGenerationStateChange={setIsBatchGenerationActive}
        />
      </div>

      {/* Right Panel: Content Display - Premium Glass Design */}
      <div className="w-full md:w-3/5 lg:w-2/3 bg-gradient-to-br from-background/60 via-background/40 to-background/30 backdrop-blur-xl flex flex-col relative overflow-hidden">
        {/* Subtle background pattern */}
        <div className="absolute inset-0 bg-premium-glow opacity-30 pointer-events-none"></div>
        <div className="relative z-10 flex flex-col h-full">
          <ContentGenerator
          selectedChapter={selectedChapter}
          bookTopic={bookTopic}
          generatedContent={generatedContent}
          onContentGenerated={handleContentGenerated}
          contentTone={contentTone}
          contentStyle={contentStyle}
          contentLanguage={contentLanguage}
          selectedLanguage={selectedLanguage}
          targetAudience={targetAudience}
          outline={outline}
          generatedChapters={generatedChapters}
          onChapterSelect={handleChapterSelect}
          nextChapterToGenerate={nextChapterToGenerate}
          workflowStep={workflowStep}
          generatedTitles={generatedTitles}
          selectedTitle={selectedTitle}
          onTitleSelect={handleTitleSelected}
          onApplyParameters={handleApplyParameters}
          onGenerateOutline={handleGenerateOutline}
          isGeneratingOutline={isGeneratingOutline}
          onSaveIdea={handleSaveIdea}
          isSavingIdea={isSavingIdea}
        />
        </div>
      </div>
    </div>
  );
}
