import * as React from 'react';
import { cn } from '@/lib/utils';

export interface CountBadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  /** The count to display */
  count: number;
  /** Whether the badge is loading */
  isLoading?: boolean;
  /** Maximum count to display before showing "99+" */
  maxCount?: number;
  /** Whether to show the badge when count is 0 */
  showZero?: boolean;
  /** Size variant of the badge */
  size?: 'sm' | 'md' | 'lg';
  /** Visual variant of the badge */
  variant?: 'default' | 'primary' | 'secondary';
}

/**
 * CountBadge component that displays a count with modern gradient styling
 * Features responsive design, accessibility support, and handles edge cases gracefully
 */
export function CountBadge({
  count,
  isLoading = false,
  maxCount = 99,
  showZero = false,
  size = 'md',
  variant = 'primary',
  className,
  ...props
}: CountBadgeProps) {
  // Don't render if count is 0 and show<PERSON>ero is false
  if (!showZero && count === 0 && !isLoading) {
    return null;
  }

  // Format the count display
  const formatCount = (num: number): string => {
    if (num === 0) return '0';
    if (num > maxCount) return `${maxCount}+`;
    return num.toString();
  };

  // Size variants
  const sizeClasses = {
    sm: 'h-4 min-w-[1rem] px-1 text-[0.625rem] leading-none',
    md: 'h-5 min-w-[1.25rem] px-1.5 text-xs leading-none',
    lg: 'h-6 min-w-[1.5rem] px-2 text-sm leading-none',
  };

  // Color variants
  const variantClasses = {
    default: 'bg-gray-500 text-white',
    primary: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md',
    secondary: 'bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
  };

  const baseClasses = [
    // Layout and positioning
    'inline-flex items-center justify-center',
    'absolute -top-2 -right-2',
    'rounded-full',
    'font-semibold',
    'transition-all duration-200',
    // Responsive behavior
    'transform-gpu',
    // Accessibility
    'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1',
  ];

  return (
    <span
      className={cn(
        baseClasses,
        sizeClasses[size],
        variantClasses[variant],
        // Loading state
        isLoading && 'animate-pulse bg-gray-300',
        // Hover effects for primary variant
        variant === 'primary' && 'hover:from-blue-600 hover:to-blue-700 hover:shadow-lg',
        // Scale effect on hover
        'hover:scale-110 active:scale-95',
        className
      )}
      // Accessibility attributes
      role="status"
      aria-live="polite"
      aria-atomic="true"
      aria-label={
        isLoading 
          ? 'Loading count...' 
          : count === 0
            ? 'No items'
            : count === 1 
              ? '1 item' 
              : `${formatCount(count)} items`
      }
      {...props}
    >
      {isLoading ? (
        // Loading indicator
        <span className="w-2 h-2 bg-current rounded-full animate-pulse" />
      ) : (
        formatCount(count)
      )}
    </span>
  );
}

/**
 * NavigationBadge component specifically designed for navigation links
 * Positions the badge relative to its parent link
 */
export function NavigationBadge({
  count,
  isLoading = false,
  className,
  ...props
}: Omit<CountBadgeProps, 'showZero'>) {
  return (
    <CountBadge
      count={count}
      isLoading={isLoading}
      showZero={false}
      size="sm"
      variant="primary"
      className={cn(
        // Position relative to parent navigation link
        'absolute -top-1 -right-1 z-10',
        // Ensure visibility on different backgrounds
        'shadow-sm border border-white/20',
        // Better responsive behavior
        'sm:-top-1.5 sm:-right-1.5 md:-top-1 md:-right-1',
        // Prevent interaction issues on mobile
        'pointer-events-none select-none',
        className
      )}
      // Enhanced accessibility for navigation context
      aria-describedby="navbar-badge-description"
      {...props}
    />
  );
}

export { CountBadge as default };