# Firebase Configuration
VITE_FIREBASE_API_KEY=AIzaSyC1EPbhsqYAFPfsxBtOF1xIbU45RoA85RA
VITE_FIREBASE_AUTH_DOMAIN=aiebookwriter.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=aiebookwriter
VITE_FIREBASE_STORAGE_BUCKET=aiebookwriter.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=428756360026
VITE_FIREBASE_APP_ID=1:428756360026:web:a2a3f41d2ec3da25143163
VITE_FIREBASE_MEASUREMENT_ID=G-TZFXM9KSTH

# NonFictionBookBuilder Environment Variables
# IMPORTANT: Replace 'your_actual_gemini_api_key_here' with your real Google Gemini API key
# Get your API key from: https://makersuite.google.com/app/apikey

# ⚠️  PASTE YOUR ACTUAL API KEY BELOW (replace the placeholder)
GEMINI_API_KEY=your_actual_gemini_api_key_here
NODE_ENV=development

# Instructions:
# 1. Replace the placeholder above with your actual Gemini API key
# 2. For Netlify deployment, also set GEMINI_API_KEY in:
#    https://app.netlify.com/projects/aiebookwriterpro/settings/env
# 3. Never commit this file to git for security

# Google Gemini API
GEMINI_API_KEY=AIzaSyCyoBAr_xqLi_nz7dZy6fLn7PV7nADVtMk