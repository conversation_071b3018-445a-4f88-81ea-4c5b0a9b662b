import { useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";

export default function UserMenu() {
  const { user, logout, sendVerification } = useAuth();
  const [open, setOpen] = useState(false);
  const anchorRef = useRef<HTMLButtonElement | null>(null);
  const [coords, setCoords] = useState<{ top: number; right: number } | null>(null);

  const email = user?.email ?? "User";
  const initials = email[0]?.toUpperCase() ?? "U";

  if (!user) return null;

  const resend = async () => {
    await sendVerification();
  };

  const computePosition = () => {
    const rect = anchorRef.current?.getBoundingClientRect();
    if (!rect) return;
    setCoords({ top: rect.bottom + 8, right: window.innerWidth - rect.right });
  };

  useEffect(() => {
    if (!open) return;
    computePosition();
    const onScroll = () => computePosition();
    const onResize = () => computePosition();
    window.addEventListener("scroll", onScroll, true);
    window.addEventListener("resize", onResize);
    return () => {
      window.removeEventListener("scroll", onScroll, true);
      window.removeEventListener("resize", onResize);
    };
  }, [open]);

  return (
    <div className="relative ml-4">
      <button
        ref={anchorRef}
        className="w-9 h-9 rounded-full bg-glass-card border border-glass flex items-center justify-center text-sm"
        onClick={() => setOpen((o) => !o)}
        aria-haspopup="menu"
        aria-expanded={open}
        aria-label="User menu"
      >
        {initials}
      </button>

      {open && coords &&
        createPortal(
          <>
            {/* click-catcher */}
            <div className="fixed inset-0" style={{ zIndex: 2147483646 }} onClick={() => setOpen(false)} />
            {/* menu */}
            <div
              className="fixed w-64 glass-card rounded-lg p-3 backdrop-blur-md border border-glass text-sm shadow-lg"
              style={{ top: coords.top, right: coords.right, zIndex: 2147483647 }}
              role="menu"
            >
              <div className="px-2 py-1">
                <div className="font-medium">{email}</div>
                <div className="text-xs text-muted-foreground">
                  {user.emailVerified ? "Verified" : "Not verified"}
                </div>
              </div>
              <div className="my-2 h-px bg-white/10" />
              <div className="flex flex-col gap-1">
                <Button variant="ghost" className="justify-start" onClick={() => (window.location.href = "/profile")}>View Profile</Button>
                {!user.emailVerified && (
                  <Button variant="ghost" className="justify-start" onClick={resend}>Resend Verification</Button>
                )}
                <Button variant="ghost" className="justify-start text-red-300" onClick={logout}>Logout</Button>
              </div>
            </div>
          </>,
          document.body
        )}
    </div>
  );
}

