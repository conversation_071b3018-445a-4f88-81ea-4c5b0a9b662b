@echo off
echo Installing Firebase CLI globally...
npm install -g firebase-tools

echo Logging into Firebase...
firebase login

echo Initializing Firebase project...
firebase use --add aiebookwriter

echo Deploying Firestore rules and indexes...
firebase deploy --only firestore

echo.
echo ========================================
echo Firebase setup complete!
echo ========================================
echo.
echo Next steps:
echo 1. Your Firestore security rules have been deployed
echo 2. Indexes have been created for optimal query performance
echo 3. Test your application - project saving should now work
echo.
echo If you still encounter issues, check:
echo 1. User authentication status in browser dev tools
echo 2. Network tab for failed requests
echo 3. Firebase console for rule validation
echo.
pause