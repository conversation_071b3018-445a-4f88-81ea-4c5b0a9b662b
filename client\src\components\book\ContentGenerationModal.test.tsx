import { useState } from "react";
import ContentGenerationModal from "./ContentGenerationModal";

export default function ContentGenerationModalTest() {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Content Generation Modal Test</h1>
      <button
        onClick={() => setIsVisible(true)}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
      >
        Show Modal
      </button>
      <button
        onClick={() => setIsVisible(false)}
        className="ml-4 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
      >
        Hide Modal
      </button>
      
      <div className="mt-8 h-96 overflow-y-auto">
        <p className="mb-4">Scroll down to test that the modal stays centered...</p>
        {[...Array(20)].map((_, i) => (
          <p key={i} className="mb-4">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
          </p>
        ))}
      </div>
      
      <ContentGenerationModal isVisible={isVisible} />
    </div>
  );
}