import * as React from "react"

import { cn } from "@/lib/utils"

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, ...props }, ref) => {
    return (
      <textarea
        className={cn(
          "flex min-h-[100px] w-full rounded-lg border-2 border-glass bg-glass-card backdrop-blur-sm px-4 py-3 text-sm font-medium text-foreground ring-offset-background placeholder:text-placeholder-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent focus-visible:ring-offset-2 focus-visible:border-accent focus-visible:bg-glass-enhanced disabled:cursor-not-allowed disabled:text-disabled-foreground disabled:bg-disabled-background disabled:border-disabled-foreground transition-all duration-300 resize-none shadow-glass hover:border-glass-subtle hover:bg-glass-enhanced",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Textarea.displayName = "Textarea"

export { Textarea }
