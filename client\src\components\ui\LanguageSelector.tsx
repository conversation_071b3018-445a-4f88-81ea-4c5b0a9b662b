import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { BookLanguage, LanguageOption } from "@shared/types";

interface LanguageSelectorProps {
  selectedLanguage: BookLanguage;
  onLanguageChange: (language: BookLanguage) => void;
  disabled?: boolean;
  className?: string;
}

// Comprehensive list of supported languages with ISO codes
const LANGUAGE_OPTIONS: LanguageOption[] = [
  { code: 'ar', name: 'Arabic', isRTL: true },
  { code: 'bn', name: 'Bengali' },
  { code: 'bg', name: 'Bulgarian' },
  { code: 'zh-CN', name: 'Chinese (Simplified)' },
  { code: 'zh-TW', name: 'Chinese (Traditional)' },
  { code: 'hr', name: 'Croatian' },
  { code: 'cs', name: 'Czech' },
  { code: 'da', name: 'Danish' },
  { code: 'nl', name: 'Dutch' },
  { code: 'en', name: 'English' },
  { code: 'et', name: 'Estonian' },
  { code: 'fi', name: 'Finnish' },
  { code: 'fr', name: 'French' },
  { code: 'de', name: 'German' },
  { code: 'el', name: 'Greek' },
  { code: 'he', name: 'Hebrew', isRTL: true },
  { code: 'hi', name: 'Hindi' },
  { code: 'hu', name: 'Hungarian' },
  { code: 'id', name: 'Indonesian' },
  { code: 'it', name: 'Italian' },
  { code: 'ja', name: 'Japanese' },
  { code: 'ko', name: 'Korean' },
  { code: 'lv', name: 'Latvian' },
  { code: 'lt', name: 'Lithuanian' },
  { code: 'no', name: 'Norwegian' },
  { code: 'pl', name: 'Polish' },
  { code: 'pt', name: 'Portuguese' },
  { code: 'ro', name: 'Romanian' },
  { code: 'ru', name: 'Russian' },
  { code: 'sr', name: 'Serbian' },
  { code: 'sk', name: 'Slovak' },
  { code: 'sl', name: 'Slovenian' },
  { code: 'es', name: 'Spanish' },
  { code: 'sw', name: 'Swahili' },
  { code: 'sv', name: 'Swedish' },
  { code: 'th', name: 'Thai' },
  { code: 'tr', name: 'Turkish' },
  { code: 'uk', name: 'Ukrainian' },
  { code: 'vi', name: 'Vietnamese' },
];

export default function LanguageSelector({
  selectedLanguage,
  onLanguageChange,
  disabled = false,
  className = ""
}: LanguageSelectorProps) {
  const selectedOption = LANGUAGE_OPTIONS.find(option => option.code === selectedLanguage);

  return (
    <div className={`space-y-2 ${className}`}>
      <Label htmlFor="language-selector" className="text-sm font-medium text-muted-foreground">
        Book Language
      </Label>
      <Select
        value={selectedLanguage}
        onValueChange={(value) => onLanguageChange(value as BookLanguage)}
        disabled={disabled}
      >
        <SelectTrigger 
          id="language-selector"
          className="w-full"
          aria-label="Select book language"
        >
          <SelectValue placeholder="Select language">
            {selectedOption ? (
              <span className={selectedOption.isRTL ? 'text-right' : 'text-left'}>
                {selectedOption.name}
              </span>
            ) : (
              'Select language'
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent className="max-h-60 overflow-y-auto">
          {LANGUAGE_OPTIONS.map((option) => (
            <SelectItem 
              key={option.code} 
              value={option.code}
              className={option.isRTL ? 'text-right' : 'text-left'}
            >
              <span className="flex items-center justify-between w-full">
                <span>{option.name}</span>
                <span className="text-xs text-muted-foreground ml-2">
                  {option.code}
                </span>
              </span>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {selectedOption?.isRTL && (
        <p className="text-xs text-muted-foreground">
          This language uses right-to-left text direction
        </p>
      )}
    </div>
  );
}

// Export the language options for use in other components if needed
export { LANGUAGE_OPTIONS };
