import { BookSize, BookSizeConstraints } from './types';

// Book size specifications
export const BOOK_SIZE_CONSTRAINTS: Record<BookSize, BookSizeConstraints> = {
  small: {
    minChapters: 5,
    maxChapters: 10,
    minSubChapters: 15,
    maxSubChapters: 30
  },
  medium: {
    minChapters: 10,
    maxChapters: 20,
    minSubChapters: 30,
    maxSubChapters: 50
  },
  large: {
    minChapters: 20,
    maxChapters: 30,
    minSubChapters: 50,
    maxSubChapters: 80
  }
};

// Get constraints for a specific book size
export function getBookSizeConstraints(size: BookSize): BookSizeConstraints {
  return BOOK_SIZE_CONSTRAINTS[size];
}

// Get display label for book size
export function getBookSizeLabel(size: BookSize): string {
  switch (size) {
    case 'small':
      return 'Small';
    case 'medium':
      return 'Medium';
    case 'large':
      return 'Large';
    default:
      return 'Small';
  }
}

// Get description for book size
export function getBookSizeDescription(size: BookSize): string {
  const constraints = getBookSizeConstraints(size);
  return `${constraints.minChapters}-${constraints.maxChapters} chapters with ${constraints.minSubChapters}-${constraints.maxSubChapters} total sub-chapters`;
}

// Adjust chapter structure to fit within size constraints
export function adjustChapterStructureToSize(
  originalChapterCount: number,
  originalSubChapterCounts: number[],
  targetSize: BookSize
): { chapterCount: number; subChapterCounts: number[] } {
  const constraints = getBookSizeConstraints(targetSize);
  
  // Calculate original total sub-chapters
  const originalTotalSubChapters = originalSubChapterCounts.reduce((sum, count) => sum + count, 0);
  
  // Adjust chapter count to fit within constraints
  let adjustedChapterCount = Math.max(
    constraints.minChapters,
    Math.min(constraints.maxChapters, originalChapterCount)
  );
  
  // Calculate target total sub-chapters within constraints
  let targetTotalSubChapters = Math.max(
    constraints.minSubChapters,
    Math.min(constraints.maxSubChapters, originalTotalSubChapters)
  );
  
  // Distribute sub-chapters proportionally
  let adjustedSubChapterCounts: number[] = [];
  
  if (adjustedChapterCount <= originalChapterCount) {
    // If we're reducing chapters, take the first N chapters and adjust their sub-chapter counts
    const selectedOriginalCounts = originalSubChapterCounts.slice(0, adjustedChapterCount);
    const originalSelectedTotal = selectedOriginalCounts.reduce((sum, count) => sum + count, 0);
    
    // Scale the sub-chapter counts proportionally
    adjustedSubChapterCounts = selectedOriginalCounts.map(count => {
      const proportion = count / originalSelectedTotal;
      return Math.max(2, Math.round(proportion * targetTotalSubChapters));
    });
  } else {
    // If we're adding chapters, extend the pattern
    const avgSubChaptersPerChapter = Math.round(targetTotalSubChapters / adjustedChapterCount);
    adjustedSubChapterCounts = new Array(adjustedChapterCount).fill(avgSubChaptersPerChapter);
    
    // Adjust to match exact target if needed
    const currentTotal = adjustedSubChapterCounts.reduce((sum, count) => sum + count, 0);
    const difference = targetTotalSubChapters - currentTotal;
    
    if (difference !== 0) {
      // Distribute the difference across chapters
      for (let i = 0; i < Math.abs(difference) && i < adjustedChapterCount; i++) {
        adjustedSubChapterCounts[i] += difference > 0 ? 1 : -1;
        adjustedSubChapterCounts[i] = Math.max(2, adjustedSubChapterCounts[i]); // Ensure minimum 2 sub-chapters
      }
    }
  }
  
  // Final validation to ensure we're within constraints
  const finalTotal = adjustedSubChapterCounts.reduce((sum, count) => sum + count, 0);
  if (finalTotal < constraints.minSubChapters || finalTotal > constraints.maxSubChapters) {
    // Fallback: create a balanced distribution
    const avgPerChapter = Math.round(
      (constraints.minSubChapters + constraints.maxSubChapters) / 2 / adjustedChapterCount
    );
    adjustedSubChapterCounts = new Array(adjustedChapterCount).fill(Math.max(2, avgPerChapter));
  }
  
  return {
    chapterCount: adjustedChapterCount,
    subChapterCounts: adjustedSubChapterCounts
  };
}

// Generate size-aware chapter structure for title generation
export function generateSizeAwareStructure(size: BookSize): { chapterCount: number; subChapterCounts: number[] } {
  const constraints = getBookSizeConstraints(size);
  
  // Generate a random chapter count within the size constraints
  const chapterCount = Math.floor(
    Math.random() * (constraints.maxChapters - constraints.minChapters + 1) + constraints.minChapters
  );
  
  // Calculate target total sub-chapters (aim for middle of range)
  const targetTotal = Math.floor(
    (constraints.minSubChapters + constraints.maxSubChapters) / 2
  );
  
  // Distribute sub-chapters with some variation
  const baseSubChaptersPerChapter = Math.floor(targetTotal / chapterCount);
  const remainder = targetTotal % chapterCount;
  
  const subChapterCounts = new Array(chapterCount).fill(baseSubChaptersPerChapter);
  
  // Distribute remainder and add some variation
  for (let i = 0; i < chapterCount; i++) {
    if (i < remainder) {
      subChapterCounts[i]++;
    }
    
    // Add some random variation (±1)
    const variation = Math.random() < 0.5 ? -1 : 1;
    subChapterCounts[i] = Math.max(2, subChapterCounts[i] + (Math.random() < 0.3 ? variation : 0));
  }
  
  return { chapterCount, subChapterCounts };
}
