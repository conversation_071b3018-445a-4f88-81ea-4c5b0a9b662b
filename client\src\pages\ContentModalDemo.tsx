import { useState } from "react";
import ContentGenerationModal from "@/components/book/ContentGenerationModal";
import { Button } from "@/components/ui/button";

export default function ContentModalDemo() {
  const [isModalVisible, setIsModalVisible] = useState(false);

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Content Generation Modal Demo</h1>
        
        <div className="mb-8 p-6 glass-card rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Test the Floating Modal</h2>
          <p className="text-muted-foreground mb-4">
            Click the button below to show the modal. Try scrolling the page while the modal is open - 
            it should remain centered on the screen at all times.
          </p>
          <Button onClick={() => setIsModalVisible(true)}>
            Show Content Generation Modal
          </Button>
        </div>

        <div className="space-y-6">
          <div className="p-6 glass-card rounded-lg">
            <h3 className="text-lg font-semibold mb-2">Section 1</h3>
            <p className="text-muted-foreground">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt 
              ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation 
              ullamco laboris nisi ut aliquip ex ea commodo consequat.
            </p>
          </div>

          <div className="p-6 glass-card rounded-lg">
            <h3 className="text-lg font-semibold mb-2">Section 2</h3>
            <p className="text-muted-foreground">
              Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat 
              nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui 
              officia deserunt mollit anim id est laborum.
            </p>
          </div>

          <div className="p-6 glass-card rounded-lg">
            <h3 className="text-lg font-semibold mb-2">Section 3</h3>
            <p className="text-muted-foreground">
              Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque 
              laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi 
              architecto beatae vitae dicta sunt explicabo.
            </p>
          </div>

          <div className="p-6 glass-card rounded-lg">
            <h3 className="text-lg font-semibold mb-2">Section 4</h3>
            <p className="text-muted-foreground">
              Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia 
              consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt. Neque porro 
              quisquam est, qui dolorem ipsum quia dolor sit amet.
            </p>
          </div>

          <div className="p-6 glass-card rounded-lg">
            <h3 className="text-lg font-semibold mb-2">Section 5</h3>
            <p className="text-muted-foreground">
              At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium 
              voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint 
              occaecati cupiditate non provident.
            </p>
          </div>
        </div>

        <div className="mt-8 p-6 glass-card rounded-lg">
          <h2 className="text-xl font-semibold mb-4">How It Works</h2>
          <ul className="list-disc pl-6 space-y-2 text-muted-foreground">
            <li>The modal uses <code className="bg-muted px-1 rounded">position: fixed</code> to stay in place during scrolling</li>
            <li>It's centered using flexbox alignment on both axes</li>
            <li>A semi-transparent backdrop covers the entire viewport</li>
            <li>The modal has a high z-index to appear above all other content</li>
            <li>Glass-morphism effects match the project's design system</li>
          </ul>
        </div>
      </div>

      <ContentGenerationModal isVisible={isModalVisible} />
    </div>
  );
}