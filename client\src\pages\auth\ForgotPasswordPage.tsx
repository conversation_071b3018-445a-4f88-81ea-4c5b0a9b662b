import { useState } from "react";
import { <PERSON> } from "wouter";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export default function ForgotPasswordPage() {
  const { resetPassword, error } = useAuth();
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage(null);
    try {
      await resetPassword(email);
      setMessage("Password reset email sent. Check your inbox.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center py-16 px-4">
      <div className="glass-card w-full max-w-md rounded-xl p-8">
        <h2 className="text-2xl font-semibold mb-1">Reset password</h2>
        <p className="text-sm text-muted-foreground mb-6">We'll send a reset link to your email</p>
        {(error || message) && (
          <div className={`mb-4 text-sm ${error ? 'text-red-400' : 'text-green-400'}`}>
            {error || message}
          </div>
        )}
        <form onSubmit={onSubmit} className="space-y-4">
          <div>
            <label className="block text-sm mb-1">Email</label>
            <Input
              className="glass-input"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              placeholder="<EMAIL>"
            />
          </div>
          <Button className="w-full gradient-button" type="submit" disabled={loading}>
            {loading ? "Sending…" : "Send reset link"}
          </Button>
        </form>
        <div className="mt-4 text-sm flex justify-between">
          <Link href="/login"><a className="text-primary hover:underline">Back to login</a></Link>
        </div>
      </div>
    </div>
  );
}

