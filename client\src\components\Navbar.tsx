import { Link } from "wouter";
import { useAuth } from "@/context/AuthContext";
import UserMenu from "@/components/UserMenu";
import { NavigationBadge } from "@/components/ui/count-badge";
import { useDataCounts } from "@/hooks/use-data-counts";

export default function Navbar() {
  const { user } = useAuth();
  const { currentProjects, savedIdeas, isLoading } = useDataCounts();
  return (
    <nav className="glass-nav shadow-lg relative z-[100000]" role="navigation" aria-label="Main navigation">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hidden description for screen readers */}
        <div id="navbar-badge-description" className="sr-only">
          Count badges show the number of saved items for each section
        </div>
        
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <svg className="h-8 w-8 text-primary" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L2 7v10l10 5 10-5V7L12 2zm0 2.5L20 9v7l-8 4-8-4V9l8-4.5z" />
              </svg>
              <span className="ml-2 text-xl font-semibold">AIeBookWriter.Pro</span>
            </div>
          </div>
          <div className="flex items-center">
            <Link href="/">
              <a className="px-3 py-2 text-sm font-medium hover:text-primary transition-colors">Home</a>
            </Link>
            <Link href="/create-ebook">
              <a className="ml-4 px-3 py-2 text-sm font-medium hover:text-primary transition-colors">Create eBook</a>
            </Link>
            {user && (
              <>
                <Link href="/current-project">
                  <a 
                    className="ml-4 px-3 py-2 text-sm font-medium hover:text-primary transition-colors relative"
                    aria-describedby={`current-project-count-${currentProjects}`}
                  >
                    Current Project
                    <NavigationBadge 
                      count={currentProjects} 
                      isLoading={isLoading}
                      id={`current-project-count-${currentProjects}`}
                      aria-label={isLoading ? 'Loading current projects count' : `${currentProjects} current ${currentProjects === 1 ? 'project' : 'projects'}`}
                    />
                  </a>
                </Link>
                <Link href="/saved-ideas">
                  <a 
                    className="ml-4 px-3 py-2 text-sm font-medium hover:text-primary transition-colors relative"
                    aria-describedby={`saved-ideas-count-${savedIdeas}`}
                  >
                    Saved Ideas
                    <NavigationBadge 
                      count={savedIdeas} 
                      isLoading={isLoading}
                      id={`saved-ideas-count-${savedIdeas}`}
                      aria-label={isLoading ? 'Loading saved ideas count' : `${savedIdeas} saved ${savedIdeas === 1 ? 'idea' : 'ideas'}`}
                    />
                  </a>
                </Link>
              </>
            )}
            {!user ? (
              <>
                <Link href="/login">
                  <a className="ml-4 px-3 py-2 text-sm font-medium hover:text-primary transition-colors">Login</a>
                </Link>
                <Link href="/signup">
                  <a className="ml-2 px-3 py-2 text-sm font-medium hover:text-primary transition-colors">Sign Up</a>
                </Link>
              </>
            ) : (
              <UserMenu />
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}
