#!/usr/bin/env node

/**
 * Netlify Deployment Script
 * 
 * This script:
 * 1. Reads environment variables from .env file
 * 2. Sets them in Netlify using the CLI
 * 3. Deploys the application
 * 
 * Usage: node deploy-netlify.js
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 Starting Netlify deployment with environment variables from .env file...\n');

// Function to parse .env file
function parseEnvFile(filePath) {
  const envVars = {};
  
  if (!fs.existsSync(filePath)) {
    console.error('❌ .env file not found at:', filePath);
    process.exit(1);
  }
  
  const envContent = fs.readFileSync(filePath, 'utf8');
  const lines = envContent.split('\n');
  
  for (const line of lines) {
    const trimmedLine = line.trim();
    if (trimmedLine && !trimmedLine.startsWith('#')) {
      const [key, ...valueParts] = trimmedLine.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim();
        // Remove quotes if present
        const cleanValue = value.replace(/^["']|["']$/g, '');
        envVars[key.trim()] = cleanValue;
      }
    }
  }
  
  return envVars;
}

// Function to execute command and handle errors
function execCommand(command, description) {
  console.log(`📋 ${description}...`);
  try {
    const output = execSync(command, { encoding: 'utf8', stdio: 'inherit' });
    console.log(`✅ ${description} completed successfully\n`);
    return output;
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    process.exit(1);
  }
}

// Main deployment function
function deploy() {
  // 1. Parse .env file
  const envFilePath = path.join(__dirname, '.env');
  console.log('📖 Reading environment variables from .env file...');
  const envVars = parseEnvFile(envFilePath);
  
  console.log('📝 Found environment variables:');
  Object.keys(envVars).forEach(key => {
    const maskedValue = key.toLowerCase().includes('key') || key.toLowerCase().includes('secret')
      ? '***MASKED***'
      : envVars[key];
    console.log(`   ${key}: ${maskedValue}`);
  });
  console.log('');

  // 2. Check if Netlify CLI is installed
  try {
    execSync('netlify --version', { encoding: 'utf8', stdio: 'pipe' });
    console.log('✅ Netlify CLI is available\n');
  } catch (error) {
    console.error('❌ Netlify CLI is not installed. Please install it with: npm install -g netlify-cli');
    process.exit(1);
  }

  // 3. Check if we're linked to a Netlify site
  try {
    execSync('netlify status', { encoding: 'utf8', stdio: 'pipe' });
    console.log('✅ Netlify site is linked\n');
  } catch (error) {
    console.error('❌ Not linked to a Netlify site. Please run "netlify link" first.');
    process.exit(1);
  }

  // 4. Set environment variables in Netlify
  console.log('🔧 Setting environment variables in Netlify...');
  for (const [key, value] of Object.entries(envVars)) {
    try {
      execSync(`netlify env:set "${key}" "${value}"`, { encoding: 'utf8', stdio: 'pipe' });
      console.log(`   ✅ Set ${key}`);
    } catch (error) {
      console.error(`   ❌ Failed to set ${key}:`, error.message);
    }
  }
  console.log('');

  // 5. Build the application
  execCommand('npm run build', 'Building application');

  // 6. Deploy to Netlify
  execCommand('netlify deploy --prod', 'Deploying to Netlify');

  console.log('🎉 Deployment completed successfully!');
  console.log('🌐 Your app should be available at your Netlify domain.');
}

// Run deployment
try {
  deploy();
} catch (error) {
  console.error('💥 Deployment failed:', error.message);
  process.exit(1);
}