import React from 'react';
import { Button } from "@/components/ui/button";
import { BookTitle } from "@shared/types";

interface BookCoverCardProps {
  title: BookTitle;
  isSelected: boolean;
  onSelect: (title: BookTitle) => void;
  onApply: (title: BookTitle) => void;
  isApplying?: boolean;
  onSaveIdea?: (title: BookTitle) => void;
  isSaving?: boolean;
  originalTopic?: string;
}

export default function BookCoverCard({
  title,
  isSelected,
  onSelect,
  onApply,
  isApplying = false,
  onSaveIdea,
  isSaving = false,
  originalTopic
}: BookCoverCardProps) {
  const handleCardClick = () => {
    if (!isSelected) {
      onSelect(title);
    }
  };

  const handleApplyClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onApply(title);
  };

  const handleSaveIdeaClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onSaveIdea) {
      onSaveIdea(title);
    }
  };

  return (
    <div
      className={`
        relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 
        hover:shadow-lg hover:scale-[1.02] transform-gpu
        ${isSelected 
          ? 'border-primary bg-gradient-to-br from-primary/10 to-primary/5 shadow-xl ring-2 ring-primary/20' 
          : 'border-muted hover:border-primary/50 bg-gradient-to-br from-card to-muted/20 hover:shadow-md'
        }
        min-h-[320px] flex flex-col justify-between
        book-cover-card
      `}
      onClick={handleCardClick}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleCardClick();
        }
      }}
      aria-label={`Select book title: ${title.title}`}
    >
      {/* Book Title - Prominent */}
      <div className="mb-4">
        <h3 className={`
          font-bold text-xl leading-tight mb-3 text-center
          ${isSelected ? 'text-primary' : 'text-foreground'}
          book-title
        `}>
          {title.title}
        </h3>
        
        {/* Chapter Information - Small font */}
        <div className="text-center mb-3">
          <p className="text-sm text-muted-foreground font-medium">
            {title.chapterCount} Chapters • {title.subChapterCounts.reduce((a, b) => a + b, 0)} Sub-chapters
          </p>
          <p className="text-xs text-muted-foreground mt-1">
            ({title.subChapterCounts.join(', ')} sub-chapters per chapter)
          </p>
        </div>
      </div>

      {/* Book Parameters - Even smaller font */}
      <div className="mb-4 space-y-2">
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div>
            <span className="font-medium text-muted-foreground">Tone:</span>
            <p className="text-foreground capitalize">{title.tone}</p>
          </div>
          <div>
            <span className="font-medium text-muted-foreground">Style:</span>
            <p className="text-foreground capitalize">{title.style}</p>
          </div>
          <div>
            <span className="font-medium text-muted-foreground">Level:</span>
            <p className="text-foreground capitalize">{title.languageLevel}</p>
          </div>
          <div>
            <span className="font-medium text-muted-foreground">Audience:</span>
            <p className="text-foreground">{title.targetAudience}</p>
          </div>
        </div>
      </div>

      {/* Book Summary */}
      <div className="mb-4 flex-grow">
        <p className="text-sm text-muted-foreground leading-relaxed text-center">
          {title.summary}
        </p>
      </div>

      {/* Save Idea and Apply Buttons - Only visible when selected */}
      {isSelected && (
        <div className="mt-4 pt-4 border-t border-primary/20 space-y-2">
          {/* Save Idea Button */}
          {onSaveIdea && (
            <Button
              onClick={handleSaveIdeaClick}
              disabled={isSaving}
              variant="outline"
              className="w-full border-2 border-primary/30 hover:border-primary/50 text-primary hover:text-primary font-medium py-2.5 shadow-md hover:shadow-lg transition-all duration-200"
              size="sm"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                  Saving Idea...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                  </svg>
                  Save Idea
                </>
              )}
            </Button>
          )}
          
          {/* Apply Button */}
          <Button
            onClick={handleApplyClick}
            disabled={isApplying}
            className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium py-2.5 shadow-md hover:shadow-lg transition-all duration-200"
            size="sm"
          >
            {isApplying ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Applying...
              </>
            ) : (
              'Apply'
            )}
          </Button>
        </div>
      )}

      {/* Selection indicator */}
      {isSelected && (
        <div className="absolute top-3 right-3">
          <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
        </div>
      )}
    </div>
  );
}
