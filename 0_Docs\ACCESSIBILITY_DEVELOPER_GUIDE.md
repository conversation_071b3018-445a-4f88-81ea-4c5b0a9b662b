# Accessibility Developer Guide - NonFictionBookBuilder

## Quick Reference for Developers

### Color Usage Guidelines

#### ✅ USE THESE CLASSES
```jsx
// For regular text
<p className="text-foreground">Main content</p>

// For muted text (WCAG compliant)
<span className="text-muted-foreground">Secondary info</span>

// For placeholders
<input placeholder="..." className="placeholder:text-placeholder-foreground" />

// For icons (improved visibility)
<Icon className="text-icon-muted" />

// For disabled states (no opacity!)
<button disabled className="disabled:text-disabled-foreground disabled:bg-disabled-background">
  Disabled But<PERSON>
</button>
```

#### ❌ AVOID THESE PATTERNS
```jsx
// Don't use opacity for disabled states
<button disabled className="disabled:opacity-50">Bad</button>

// Don't use low-contrast combinations
<span className="text-gray-400 bg-gray-900">Poor contrast</span>

// Don't reduce icon visibility with opacity
<Icon className="opacity-50">Bad icon</Icon>
```

### New Accessibility Classes

#### Utility Classes
```css
.sr-only              /* Screen reader only */
.focus-ring           /* Consistent focus styling */
.text-high-contrast   /* Enhanced visibility */
.btn-accessible       /* Accessible button base */
.interactive-hover    /* Accessible hover states */
```

#### Usage Examples
```jsx
// Screen reader only text
<span className="sr-only">Additional context for screen readers</span>

// Enhanced focus
<button className="focus-ring">Accessible button</button>

// High contrast text when needed
<h1 className="text-high-contrast">Important heading</h1>
```

### Color Variables Reference

#### CSS Custom Properties (HSL Values)
```css
/* Text Colors */
--foreground: 220 15% 90%;              /* Main text - excellent contrast */
--muted-foreground: 220 15% 78%;       /* Secondary text - WCAG AA compliant */
--placeholder-foreground: 220 15% 72%; /* Form placeholders - good contrast */
--icon-muted: 220 15% 75%;             /* Icons - visible and accessible */

/* Disabled States */
--disabled-foreground: 220 15% 60%;    /* Disabled text */
--disabled-background: 220 15% 20%;    /* Disabled backgrounds */

/* Enhanced Glass Effects */
--glass-enhanced: rgba(255, 255, 255, 0.08); /* Better contrast glass */
```

### Component Patterns

#### Accessible Buttons
```jsx
const AccessibleButton = ({ children, disabled, ...props }) => (
  <button
    className={cn(
      "btn-accessible",
      disabled && "text-disabled-foreground bg-disabled-background"
    )}
    disabled={disabled}
    {...props}
  >
    {children}
  </button>
);
```

#### Form Fields with Good Contrast
```jsx
const AccessibleInput = ({ placeholder, ...props }) => (
  <input
    className="placeholder:text-placeholder-foreground focus-ring"
    placeholder={placeholder}
    {...props}
  />
);
```

#### Icons with Proper Visibility
```jsx
const AccessibleIcon = ({ className, ...props }) => (
  <Icon
    className={cn("text-icon-muted hover:text-foreground transition-colors", className)}
    {...props}
  />
);
```

### Testing Your Changes

#### Before Committing
1. **Contrast Check**: Use [WebAIM Contrast Checker](https://webaim.org/resources/contrastchecker/)
2. **Keyboard Test**: Navigate with Tab/Shift+Tab only
3. **Focus Visibility**: Ensure all interactive elements show focus rings
4. **Screen Reader**: Test with built-in screen readers

#### Automated Testing
```bash
# Run accessibility tests
npm run test:a11y

# Check with Lighthouse
npm run audit:accessibility
```

### Browser DevTools Tips

#### Chrome DevTools
1. Open Elements panel
2. Computed styles > Show all
3. Look for contrast ratio indicators
4. Use "Rendering" tab > "Emulate vision deficiencies"

#### Firefox DevTools
1. Open Inspector
2. Click the universal access icon in the toolbar
3. Use contrast ratio information in color picker

### Common Pitfalls

#### ❌ Don't Do This
```jsx
// Using opacity for important UI states
<button className="disabled:opacity-50">Bad</button>

// Low contrast text combinations
<p className="text-gray-500 bg-gray-800">Hard to read</p>

// Missing focus indicators
<div onClick={handler} className="cursor-pointer">No focus ring</div>

// Invisible placeholder text
<input placeholder="..." className="placeholder:text-gray-600" />
```

#### ✅ Do This Instead
```jsx
// Color-based disabled states
<button className="disabled:text-disabled-foreground disabled:bg-disabled-background">
  Good
</button>

// High contrast combinations
<p className="text-muted-foreground">Readable text</p>

// Proper focus management
<button className="focus-ring">Accessible button</button>

// Visible placeholders
<input placeholder="..." className="placeholder:text-placeholder-foreground" />
```

### Responsive Accessibility

#### Mobile Considerations
```css
@media (max-width: 768px) {
  /* Ensure touch targets are at least 44px */
  button, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Larger text for readability */
  .mobile-enhanced {
    font-size: 1.1em;
    line-height: 1.5;
  }
}
```

### High Contrast Mode

#### Automatic Enhancement
The application automatically adjusts for users with `prefers-contrast: high`:

```css
@media (prefers-contrast: high) {
  :root {
    --foreground: 220 15% 95%;
    --muted-foreground: 220 15% 85%;
    --border: 220 15% 50%;
  }
}
```

### Code Review Checklist

When reviewing PRs, check for:
- [ ] No `opacity` used for disabled states
- [ ] All interactive elements have focus indicators
- [ ] Text contrast meets WCAG AA (4.5:1 for normal, 3:1 for large)
- [ ] Icons use `text-icon-muted` or better
- [ ] Form fields use `placeholder:text-placeholder-foreground`
- [ ] New colors are added to the design system, not hardcoded

### Resources

- [WCAG 2.1 Quick Reference](https://www.w3.org/WAI/WCAG21/quickref/)
- [WebAIM Contrast Checker](https://webaim.org/resources/contrastchecker/)
- [A11Y Project Checklist](https://www.a11yproject.com/checklist/)
- [MDN Accessibility Guide](https://developer.mozilla.org/en-US/docs/Web/Accessibility)

### Getting Help

For accessibility questions:
1. Check this guide first
2. Test with automated tools
3. Consult the main accessibility audit report
4. When in doubt, prioritize higher contrast and clearer focus indicators