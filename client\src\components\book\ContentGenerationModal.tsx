import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";

interface ContentGenerationModalProps {
  isVisible: boolean;
  progress?: string;
  currentStep?: number;
  totalSteps?: number;
  contentParams?: {
    tone: string;
    style: string;
    language: string;
    targetLanguage: string;
  };
}

export default function ContentGenerationModal({ 
  isVisible, 
  progress = "Generating content...",
  currentStep = 0,
  totalSteps = 5,
  contentParams
}: ContentGenerationModalProps) {
  const [isVisibleState, setIsVisibleState] = useState(isVisible);

  useEffect(() => {
    setIsVisibleState(isVisible);
  }, [isVisible]);

  if (!isVisibleState) return null;

  // Dynamic progress steps based on actual generation process
  const progressSteps = [
    { title: "Analyzing chapter structure", description: "Understanding the context and position of this subchapter" },
    { title: "Researching topic relevance", description: "Gathering relevant information for your content" },
    { title: "Crafting engaging narrative", description: "Creating compelling content with your selected style" },
    { title: "Applying writing parameters", description: `Using ${contentParams?.tone || 'professional'} tone with ${contentParams?.style || 'descriptive'} style` },
    { title: "Finalizing content", description: "Ensuring quality and proper formatting" }
  ];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center overflow-hidden">
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/70 backdrop-blur-sm" />
      
      {/* Modal */}
      <div className="relative glass-card rounded-xl p-6 w-full max-w-md mx-4 shadow-2xl border border-white/20 transform transition-all duration-300 ease-in-out">
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-xl font-bold text-center">Writing chapter content...</h2>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="w-full bg-gray-700 rounded-full h-2.5">
            <div 
              className="bg-accent h-2.5 rounded-full transition-all duration-500 ease-out" 
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            ></div>
          </div>
          <div className="text-center text-sm text-muted-foreground mt-2">
            Step {currentStep} of {totalSteps}
          </div>
        </div>

        {/* Main Section */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-4 text-center">{progress}</h3>
          
          {/* Progress Steps */}
          <div className="space-y-3 mb-6">
            {progressSteps.map((step, index) => (
              <div 
                key={index} 
                className={`flex items-start transition-all duration-300 ${
                  index < currentStep ? 'opacity-70' : 
                  index === currentStep ? 'opacity-100' : 'opacity-50'
                }`}
              >
                <div className={`w-2 h-2 rounded-full mr-3 mt-1.5 flex-shrink-0 ${
                  index < currentStep ? 'bg-green-500' : 
                  index === currentStep ? 'bg-accent animate-pulse' : 'bg-gray-500'
                }`}></div>
                <div>
                  <div className={`font-medium ${
                    index === currentStep ? 'text-foreground' : 'text-muted-foreground'
                  }`}>
                    {step.title}
                  </div>
                  {index === currentStep && (
                    <div className="text-sm text-muted-foreground mt-1">
                      {step.description}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Style Section */}
          {contentParams && (
            <div className="mb-6">
              <h4 className="font-medium mb-2">Writing Parameters</h4>
              <div className="flex flex-wrap gap-2">
                <span className="px-3 py-1 bg-white/10 rounded-full text-sm capitalize">
                  {contentParams.tone || 'Professional'}
                </span>
                <span className="px-3 py-1 bg-white/10 rounded-full text-sm capitalize">
                  {contentParams.style || 'Descriptive'}
                </span>
                <span className="px-3 py-1 bg-white/10 rounded-full text-sm capitalize">
                  {contentParams.language || 'Intermediate'}
                </span>
                {contentParams.targetLanguage && contentParams.targetLanguage !== 'en' && (
                  <span className="px-3 py-1 bg-white/10 rounded-full text-sm capitalize">
                    {contentParams.targetLanguage}
                  </span>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="text-center text-sm text-muted-foreground">
          <p>Our AI is carefully crafting your content to ensure it meets the highest quality standards.</p>
        </div>
      </div>
    </div>
  );
}