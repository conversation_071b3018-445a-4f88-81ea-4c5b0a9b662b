import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const skeletonVariants = cva(
  "animate-shimmer bg-shimmer-gradient bg-glass-skeleton backdrop-blur-sm rounded-md",
  {
    variants: {
      variant: {
        default: "bg-shimmer-gradient",
        glass: "bg-glass-skeleton backdrop-blur-sm border border-glass-subtle",
        card: "bg-glass-card backdrop-blur-md border border-glass rounded-lg",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

export interface SkeletonProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof skeletonVariants> {}

const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, variant, ...props }, ref) => {
    // Respect user's motion preferences
    const prefersReducedMotion = React.useMemo(() => {
      if (typeof window !== "undefined") {
        return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
      }
      return false;
    }, []);

    return (
      <div
        className={cn(
          skeletonVariants({ variant }),
          prefersReducedMotion && "!animate-none opacity-60",
          className
        )}
        style={{
          backgroundSize: "200px 100%",
          backgroundRepeat: "no-repeat",
        }}
        ref={ref}
        {...props}
      />
    );
  }
);

Skeleton.displayName = "Skeleton";

// Specialized skeleton components for different content types

interface OutlineSkeletonProps {
  chapters?: number;
  subchaptersPerChapter?: number;
  className?: string;
}

const OutlineSkeleton = React.forwardRef<HTMLDivElement, OutlineSkeletonProps>(
  ({ chapters = 3, subchaptersPerChapter = 4, className, ...props }, ref) => {
    const prefersReducedMotion = React.useMemo(() => {
      if (typeof window !== "undefined") {
        return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
      }
      return false;
    }, []);

    return (
      <div className={cn("space-y-4 p-4", className)} ref={ref} {...props}>
        {Array.from({ length: chapters }, (_, chapterIndex) => (
          <div key={chapterIndex} className="space-y-3">
            {/* Chapter title skeleton */}
            <div className="flex items-center space-x-3">
              <Skeleton 
                variant="glass" 
                className="h-6 w-8 flex-shrink-0"
                style={{
                  animationDelay: prefersReducedMotion ? "0s" : `${chapterIndex * 0.1}s`,
                }}
              />
              <Skeleton 
                variant="glass" 
                className="h-6 flex-1 max-w-xs"
                style={{
                  animationDelay: prefersReducedMotion ? "0s" : `${chapterIndex * 0.1 + 0.05}s`,
                }}
              />
            </div>
            
            {/* Subchapter skeletons */}
            <div className="ml-8 space-y-2">
              {Array.from({ length: subchaptersPerChapter }, (_, subIndex) => (
                <Skeleton
                  key={subIndex}
                  variant="default"
                  className="h-4 w-full max-w-md"
                  style={{
                    animationDelay: prefersReducedMotion ? "0s" : `${chapterIndex * 0.1 + subIndex * 0.05}s`,
                  }}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    );
  }
);

OutlineSkeleton.displayName = "OutlineSkeleton";

interface ContentSkeletonProps {
  lines?: number;
  paragraphs?: number;
  className?: string;
}

const ContentSkeleton = React.forwardRef<HTMLDivElement, ContentSkeletonProps>(
  ({ lines = 8, paragraphs = 3, className, ...props }, ref) => {
    const prefersReducedMotion = React.useMemo(() => {
      if (typeof window !== "undefined") {
        return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
      }
      return false;
    }, []);

    return (
      <div className={cn("space-y-6 p-6", className)} ref={ref} {...props}>
        {/* Title skeleton */}
        <Skeleton variant="glass" className="h-8 w-3/4 mx-auto" />
        
        {/* Content paragraphs */}
        {Array.from({ length: paragraphs }, (_, paragraphIndex) => (
          <div key={paragraphIndex} className="space-y-3">
            {Array.from({ length: Math.ceil(lines / paragraphs) }, (_, lineIndex) => {
              const isLastLine = lineIndex === Math.ceil(lines / paragraphs) - 1;
              const width = isLastLine ? "w-2/3" : "w-full";
              
              return (
                <Skeleton
                  key={lineIndex}
                  variant="default"
                  className={cn("h-4", width)}
                  style={{
                    animationDelay: prefersReducedMotion 
                      ? "0s" 
                      : `${(paragraphIndex * Math.ceil(lines / paragraphs) + lineIndex) * 0.1}s`,
                  }}
                />
              );
            })}
          </div>
        ))}
      </div>
    );
  }
);

ContentSkeleton.displayName = "ContentSkeleton";

interface TitleListSkeletonProps {
  count?: number;
  className?: string;
}

const TitleListSkeleton = React.forwardRef<HTMLDivElement, TitleListSkeletonProps>(
  ({ count = 5, className, ...props }, ref) => {
    const prefersReducedMotion = React.useMemo(() => {
      if (typeof window !== "undefined") {
        return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
      }
      return false;
    }, []);

    return (
      <div className={cn("space-y-3 p-4", className)} ref={ref} {...props}>
        {Array.from({ length: count }, (_, index) => (
          <div 
            key={index} 
            className="space-y-2 p-4 border border-glass rounded-lg bg-glass-card backdrop-blur-sm"
          >
            {/* Title skeleton */}
            <Skeleton
              variant="glass"
              className="h-5 w-4/5"
              style={{
                animationDelay: prefersReducedMotion ? "0s" : `${index * 0.1}s`,
              }}
            />
            
            {/* Description skeleton */}
            <Skeleton
              variant="default"
              className="h-3 w-full"
              style={{
                animationDelay: prefersReducedMotion ? "0s" : `${index * 0.1 + 0.05}s`,
              }}
            />
            <Skeleton
              variant="default"
              className="h-3 w-2/3"
              style={{
                animationDelay: prefersReducedMotion ? "0s" : `${index * 0.1 + 0.1}s`,
              }}
            />
          </div>
        ))}
      </div>
    );
  }
);

TitleListSkeleton.displayName = "TitleListSkeleton";

interface ButtonSkeletonProps {
  className?: string;
}

const ButtonSkeleton = React.forwardRef<HTMLDivElement, ButtonSkeletonProps>(
  ({ className, ...props }, ref) => {
    return (
      <Skeleton
        variant="glass"
        className={cn("h-10 w-32 rounded-md", className)}
        ref={ref}
        {...props}
      />
    );
  }
);

ButtonSkeleton.displayName = "ButtonSkeleton";

export { 
  Skeleton, 
  OutlineSkeleton, 
  ContentSkeleton, 
  TitleListSkeleton, 
  ButtonSkeleton,
  skeletonVariants 
};