import { auth } from '@/lib/firebase';
import { onAuthStateChanged } from 'firebase/auth';

/**
 * Debug helper to check authentication status
 */
export function debugAuth() {
  console.log('=== Firebase Auth Debug ===');
  console.log('Current user:', auth.currentUser);
  console.log('User ID:', auth.currentUser?.uid);
  console.log('Email verified:', auth.currentUser?.emailVerified);
  console.log('Access token available:', !!auth.currentUser?.accessToken);
  
  // Listen for auth state changes
  const unsubscribe = onAuthStateChanged(auth, (user) => {
    console.log('Auth state changed:', {
      user: user?.uid,
      email: user?.email,
      verified: user?.emailVerified,
      isAnonymous: user?.isAnonymous
    });
  });
  
  // Return unsubscribe function
  return unsubscribe;
}

/**
 * Test Firestore connection with current auth
 */
export async function testFirestoreConnection() {
  try {
    console.log('=== Testing Firestore Connection ===');
    
    if (!auth.currentUser) {
      console.error('❌ No authenticated user');
      return false;
    }
    
    console.log('✅ User authenticated:', auth.currentUser.uid);
    
    // Try to get an auth token
    const token = await auth.currentUser.getIdToken();
    console.log('✅ Auth token obtained:', token ? 'Yes' : 'No');
    
    return true;
  } catch (error) {
    console.error('❌ Firestore connection test failed:', error);
    return false;
  }
}