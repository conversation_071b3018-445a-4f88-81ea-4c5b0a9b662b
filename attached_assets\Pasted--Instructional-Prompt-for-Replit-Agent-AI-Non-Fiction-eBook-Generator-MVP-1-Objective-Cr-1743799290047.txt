# Instructional Prompt for Replit Agent: AI Non-Fiction eBook Generator MVP

## 1. Objective

Create a full-stack web application MVP (Minimum Viable Product) using the Replit platform. This application will function as an AI-powered assistant for writing non-fiction eBooks, specifically targeting Amazon KDP authors.

**Core Functionality:**
1.  Accept user input defining a book topic or keywords.
2.  Utilize the Google Gemini API to generate a detailed, hierarchical book outline (main chapters with corresponding sub-chapters) based on the user's input. The outline must be returned in a specific JSON format.
3.  Allow the user to select individual sub-chapters from the generated outline.
4.  Upon selection, use the Google Gemini API again to generate the textual content for the chosen sub-chapter, using the main chapter and book topic as context.
5.  Display the generated outline and content within a user-friendly interface featuring a dark theme.

## 2. Technology Stack

*   **Backend:** Node.js with the Express.js framework.
*   **Frontend:** React (preferably set up using Vite for speed and modern tooling).
*   **AI Service:** Google Gemini API (via `@google/generative-ai` Node.js SDK).
*   **UI Library:** A React component library that supports dark themes well (e.g., Material UI, Chakra UI, or Ant Design - please choose one and use it consistently).
*   **Frontend Routing:** `react-router-dom`.
*   **API Communication:** `axios` or `fetch` API for frontend-backend communication.

## 3. Key Requirements & Features

### 3.1. Backend (Node.js/Express)

*   **API Key:** Use .env to securely store and access the `GEMINI_API_KEY`. Do not hardcode it.
*   **CORS:** Enable CORS middleware to allow requests from the frontend.
*   **API Endpoints:**
    *   `POST /api/generate-outline`
        *   **Input:** `{ "userInput": "string" }`
        *   **Output (Success):** `{ "outline": { "chapters": [{ "title": "string", "subchapters": ["string", ...] }, ...] } }` (Strictly follow this JSON structure).
        *   **Logic:** Construct a detailed prompt for Gemini requesting the hierarchical outline in the specified JSON format. Handle potential errors from the API or during JSON parsing.
    *   `POST /api/generate-chapter`
        *   **Input:** `{ "subChapterTitle": "string", "mainChapterTitle": "string", "bookTopic": "string" }`
        *   **Output (Success):** `{ "subChapterContent": "string" }`
        *   **Logic:** Construct a focused prompt for Gemini to generate content *only* for the given sub-chapter, using the main chapter and book topic for context. Handle potential API errors.
*   **Error Handling:** Implement basic error handling for API calls, request validation, and server errors. Return meaningful JSON error responses.

### 3.2. Frontend (React)

*   **Dark Theme:** The entire application must use a dark color scheme, configured via the chosen UI library's theme provider.
*   **Routing:** Set up two main routes:
    *   `/`: Landing Page (`HomePage.jsx`) - Introduce the app, highlight features, provide a link/button to the main app. Must be visually appealing.
    *   `/app`: Main Application Page (`AppPage.jsx`) - The core workspace.
*   **Main App UI (`AppPage.jsx`):**
    *   **Input:** A text area for the user to enter their book topic/keywords and a button to trigger outline generation.
    *   **Outline Display:** Use an **Accordion** component. Each main chapter title is an Accordion header. Expanding a header reveals a list of its sub-chapter titles.
    *   **Interaction:** Each sub-chapter title in the list should be clickable or have an adjacent button to trigger content generation for *that specific sub-chapter*.
    *   **Content Display:** An area to display the generated text content for the selected sub-chapter. Clearly label which sub-chapter's content is shown.
    *   **Feedback:** Implement clear loading indicators (e.g., spinners) during API calls and visual cues (e.g., checkmarks) for successfully generated sub-chapters. Display user-friendly error messages if generation fails.
*   **Component Structure:** Organize components logically (e.g., `layout`, `common`, `writer`, `landing`).

## 4. Instructions for Replit Agent

Please generate a plan to build this application step-by-step. I expect you to:

1.  Set up the project structure with Node.js/Express backend and React/Vite frontend.
2.  Install all necessary dependencies (`express`, `@google/generative-ai`, `cors`, `react-router-dom`, `axios`, UI library packages).
3.  Implement the backend API endpoints as specified, including Gemini API integration and secure API key handling via Replit Secrets. Remember the specific JSON structure for the outline.
4.  Implement the frontend components, routing, dark theme, and UI according to the requirements, focusing on the Accordion display for the outline and the sub-chapter generation workflow.
5.  Ensure proper communication between the frontend and backend.
6.  Implement basic loading states and error handling on both frontend and backend.

NOTE: Use following API key and model for testing:
Google Gemini API: "AIzaSyCyoBAr_xqLi_nz7dZy6fLn7PV7nADVtMk"
Model: "gemini-2.0-flash"