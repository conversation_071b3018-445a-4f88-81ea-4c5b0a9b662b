import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";
import { CheckIcon } from "lucide-react";

// Circular Progress Ring Component
interface CircularProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  progress: number; // 0-100
  size?: "sm" | "md" | "lg" | "xl";
  strokeWidth?: number;
  showPercentage?: boolean;
  label?: string;
  variant?: "default" | "glass" | "gradient";
}

const CircularProgress = React.forwardRef<HTMLDivElement, CircularProgressProps>(
  ({ 
    progress, 
    size = "md", 
    strokeWidth, 
    showPercentage = true, 
    label,
    variant = "default",
    className,
    ...props 
  }, ref) => {
    // Size configurations
    const sizeConfig = {
      sm: { diameter: 40, stroke: strokeWidth || 2, text: "text-xs" },
      md: { diameter: 60, stroke: strokeWidth || 3, text: "text-sm" },
      lg: { diameter: 80, stroke: strokeWidth || 4, text: "text-base" },
      xl: { diameter: 120, stroke: strokeWidth || 5, text: "text-lg" }
    };

    const config = sizeConfig[size];
    const radius = (config.diameter - config.stroke * 2) / 2;
    const circumference = 2 * Math.PI * radius;
    const offset = circumference - (progress / 100) * circumference;

    // Respect user's motion preferences
    const prefersReducedMotion = React.useMemo(() => {
      if (typeof window !== "undefined") {
        return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
      }
      return false;
    }, []);

    // Variant-specific styling
    const variantStyles = {
      default: {
        track: "stroke-muted",
        progress: "stroke-blue-500",
        background: "bg-transparent"
      },
      glass: {
        track: "stroke-white/20",
        progress: "stroke-blue-400",
        background: "bg-glass-card backdrop-blur-sm border border-glass rounded-full"
      },
      gradient: {
        track: "stroke-muted",
        progress: "stroke-url(#gradient)",
        background: "bg-transparent"
      }
    };

    const styles = variantStyles[variant];

    return (
      <div 
        ref={ref}
        className={cn(
          "relative inline-flex items-center justify-center",
          styles.background,
          className
        )}
        style={{ 
          width: config.diameter + (variant === "glass" ? 16 : 0), 
          height: config.diameter + (variant === "glass" ? 16 : 0) 
        }}
        {...props}
      >
        <svg
          width={config.diameter}
          height={config.diameter}
          className="transform -rotate-90"
          viewBox={`0 0 ${config.diameter} ${config.diameter}`}
        >
          {/* Gradient definition */}
          {variant === "gradient" && (
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#3b82f6" />
                <stop offset="100%" stopColor="#8b5cf6" />
              </linearGradient>
            </defs>
          )}
          
          {/* Track circle */}
          <circle
            cx={config.diameter / 2}
            cy={config.diameter / 2}
            r={radius}
            stroke="currentColor"
            strokeWidth={config.stroke}
            fill="none"
            className={styles.track}
          />
          
          {/* Progress circle */}
          <circle
            cx={config.diameter / 2}
            cy={config.diameter / 2}
            r={radius}
            stroke="currentColor"
            strokeWidth={config.stroke}
            fill="none"
            strokeDasharray={circumference}
            strokeDashoffset={offset}
            strokeLinecap="round"
            className={cn(
              styles.progress,
              !prefersReducedMotion && "transition-all duration-500 ease-out"
            )}
          />
        </svg>
        
        {/* Center content */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          {showPercentage && (
            <span className={cn("font-semibold text-foreground", config.text)}>
              {Math.round(progress)}%
            </span>
          )}
          {label && (
            <span className="text-xs text-muted-foreground text-center">
              {label}
            </span>
          )}
        </div>
      </div>
    );
  }
);

CircularProgress.displayName = "CircularProgress";

// Linear Progress Bar Component
interface LinearProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  progress: number; // 0-100
  variant?: "default" | "glass" | "gradient";
  size?: "sm" | "md" | "lg";
  showPercentage?: boolean;
  label?: string;
  animated?: boolean;
}

const LinearProgress = React.forwardRef<HTMLDivElement, LinearProgressProps>(
  ({ 
    progress, 
    variant = "default", 
    size = "md", 
    showPercentage = true,
    label,
    animated = true,
    className,
    ...props 
  }, ref) => {
    // Size configurations
    const heightConfig = {
      sm: "h-2",
      md: "h-3", 
      lg: "h-4"
    };

    // Respect user's motion preferences
    const prefersReducedMotion = React.useMemo(() => {
      if (typeof window !== "undefined") {
        return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
      }
      return false;
    }, []);

    // Variant-specific styling
    const variantStyles = {
      default: {
        track: "bg-muted",
        progress: "bg-blue-500"
      },
      glass: {
        track: "bg-glass-card backdrop-blur-sm border border-glass",
        progress: "bg-gradient-to-r from-blue-400 to-blue-600"
      },
      gradient: {
        track: "bg-muted",
        progress: "bg-gradient-to-r from-blue-500 to-purple-600"
      }
    };

    const styles = variantStyles[variant];

    return (
      <div ref={ref} className={cn("w-full", className)} {...props}>
        {/* Label and percentage */}
        {(label || showPercentage) && (
          <div className="flex justify-between items-center mb-2">
            {label && (
              <span className="text-sm font-medium text-foreground">{label}</span>
            )}
            {showPercentage && (
              <span className="text-sm text-muted-foreground">
                {Math.round(progress)}%
              </span>
            )}
          </div>
        )}
        
        {/* Progress bar */}
        <div className={cn(
          "w-full rounded-full overflow-hidden",
          heightConfig[size],
          styles.track
        )}>
          <div
            className={cn(
              "h-full rounded-full transition-all ease-out",
              styles.progress,
              animated && !prefersReducedMotion && "duration-500",
              variant === "glass" && "backdrop-blur-sm"
            )}
            style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
          >
            {/* Animated shimmer effect */}
            {animated && !prefersReducedMotion && (
              <div className="h-full w-full bg-shimmer-gradient bg-[length:200%_100%] animate-shimmer opacity-30" />
            )}
          </div>
        </div>
      </div>
    );
  }
);

LinearProgress.displayName = "LinearProgress";

// Step Indicator Component
interface StepIndicatorProps extends React.HTMLAttributes<HTMLDivElement> {
  steps: Array<{
    label: string;
    description?: string;
    completed?: boolean;
  }>;
  currentStep: number;
  variant?: "default" | "glass";
  orientation?: "horizontal" | "vertical";
}

const StepIndicator = React.forwardRef<HTMLDivElement, StepIndicatorProps>(
  ({ 
    steps, 
    currentStep, 
    variant = "default", 
    orientation = "horizontal",
    className,
    ...props 
  }, ref) => {
    // Respect user's motion preferences
    const prefersReducedMotion = React.useMemo(() => {
      if (typeof window !== "undefined") {
        return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
      }
      return false;
    }, []);

    const isHorizontal = orientation === "horizontal";

    return (
      <div 
        ref={ref}
        className={cn(
          "flex",
          isHorizontal ? "items-center space-x-4" : "flex-col space-y-4",
          className
        )}
        {...props}
      >
        {steps.map((step, index) => {
          const isCompleted = step.completed || index < currentStep;
          const isCurrent = index === currentStep;
          const isUpcoming = index > currentStep;

          return (
            <React.Fragment key={index}>
              <div className={cn(
                "flex items-center",
                !isHorizontal && "w-full"
              )}>
                {/* Step circle */}
                <div className={cn(
                  "relative flex items-center justify-center rounded-full border-2 transition-all duration-300",
                  "w-8 h-8",
                  isCompleted && "bg-green-500 border-green-500 text-white",
                  isCurrent && variant === "default" && "bg-blue-500 border-blue-500 text-white",
                  isCurrent && variant === "glass" && "bg-glass-card backdrop-blur-sm border-blue-400 text-blue-400",
                  isUpcoming && "bg-muted border-muted-foreground/30 text-muted-foreground",
                  !prefersReducedMotion && isCurrent && "animate-pulse-glow"
                )}>
                  {isCompleted ? (
                    <CheckIcon className="w-4 h-4" />
                  ) : (
                    <span className="text-sm font-medium">{index + 1}</span>
                  )}
                </div>

                {/* Step content */}
                <div className={cn(
                  "ml-3 flex-1 min-w-0",
                  !isHorizontal && "mb-2"
                )}>
                  <p className={cn(
                    "text-sm font-medium transition-colors duration-200",
                    isCompleted && "text-green-600",
                    isCurrent && "text-blue-600",
                    isUpcoming && "text-muted-foreground"
                  )}>
                    {step.label}
                  </p>
                  {step.description && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {step.description}
                    </p>
                  )}
                </div>
              </div>

              {/* Connector line */}
              {index < steps.length - 1 && (
                <div className={cn(
                  "transition-colors duration-300",
                  isHorizontal ? "h-0.5 flex-1 min-w-8" : "w-0.5 h-6 ml-4",
                  isCompleted ? "bg-green-500" : "bg-muted"
                )} />
              )}
            </React.Fragment>
          );
        })}
      </div>
    );
  }
);

StepIndicator.displayName = "StepIndicator";

// Generation Progress Component (specialized for book generation)
interface GenerationProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  totalSteps: number;
  currentStep: number;
  currentStepProgress: number; // 0-100 for current step
  stepLabels?: string[];
  variant?: "default" | "glass";
}

const GenerationProgress = React.forwardRef<HTMLDivElement, GenerationProgressProps>(
  ({ 
    totalSteps, 
    currentStep, 
    currentStepProgress, 
    stepLabels,
    variant = "default",
    className,
    ...props 
  }, ref) => {
    const overallProgress = ((currentStep - 1) / totalSteps) * 100 + (currentStepProgress / totalSteps);
    
    const defaultLabels = [
      "Analyzing Topic",
      "Generating Titles", 
      "Creating Outline",
      "Writing Content",
      "Finalizing"
    ];

    const labels = stepLabels || defaultLabels.slice(0, totalSteps);

    return (
      <div ref={ref} className={cn("space-y-4", className)} {...props}>
        {/* Overall progress */}
        <LinearProgress
          progress={overallProgress}
          variant={variant}
          label="Overall Progress"
          showPercentage={true}
        />

        {/* Current step progress */}
        <LinearProgress
          progress={currentStepProgress}
          variant={variant}
          size="sm"
          label={`Step ${currentStep}: ${labels[currentStep - 1] || `Step ${currentStep}`}`}
          showPercentage={true}
        />

        {/* Step indicator */}
        <StepIndicator
          steps={labels.map((label, index) => ({
            label,
            completed: index < currentStep - 1,
          }))}
          currentStep={currentStep - 1}
          variant={variant}
          orientation="horizontal"
        />
      </div>
    );
  }
);

GenerationProgress.displayName = "GenerationProgress";

export {
  CircularProgress,
  LinearProgress, 
  StepIndicator,
  GenerationProgress
};